Set WshShell = CreateObject("WScript.Shell")
Set fso = CreateObject("Scripting.FileSystemObject")

' Get the directory where this script is located
scriptDir = fso.GetParentFolderName(WScript.ScriptFullName)

' Define the batch file to run
batchFile = scriptDir & "\تشغيل التطبيق.bat"

' Check if the batch file exists
If fso.FileExists(batchFile) Then
    ' Run the batch file hidden (no window)
    WshShell.Run """" & batchFile & """", 0, False
Else
    ' Fallback: try to open index.html
    htmlFile = scriptDir & "\index.html"
    If fso.FileExists(htmlFile) Then
        WshShell.Run """" & htmlFile & """", 1, False
    Else
        MsgBox "لم يتم العثور على ملفات التطبيق", 16, "خطأ"
    End If
End If
