@echo off
setlocal enabledelayedexpansion
title Final Package Creator - Future Fuel Corporation

echo ========================================
echo   Final Package Creator
echo   Future Fuel Corporation
echo ========================================
echo.

set "PACKAGE_NAME=Future Fuel Gas Shop Management - Complete Package"
set "DESKTOP_PACKAGE=%USERPROFILE%\Desktop\%PACKAGE_NAME%"
set "SOURCE_DIR=%USERPROFILE%\AppData\Local\GasShopManagement"
set "CURRENT_DIR=%CD%"

echo Creating complete package on desktop...
echo Package location: %DESKTOP_PACKAGE%
echo.

echo [1/8] Creating package structure...
if exist "%DESKTOP_PACKAGE%" rmdir /s /q "%DESKTOP_PACKAGE%"
mkdir "%DESKTOP_PACKAGE%"
mkdir "%DESKTOP_PACKAGE%\Application"
mkdir "%DESKTOP_PACKAGE%\Application\assets"
mkdir "%DESKTOP_PACKAGE%\Application\styles"
mkdir "%DESKTOP_PACKAGE%\Application\scripts"
mkdir "%DESKTOP_PACKAGE%\Application\src"
mkdir "%DESKTOP_PACKAGE%\Installers"
mkdir "%DESKTOP_PACKAGE%\Tools"
mkdir "%DESKTOP_PACKAGE%\Documentation"

echo [2/8] Copying application files...
if exist "%SOURCE_DIR%\index.html" copy "%SOURCE_DIR%\index.html" "%DESKTOP_PACKAGE%\Application\" >nul 2>&1
if exist "%SOURCE_DIR%\main.js" copy "%SOURCE_DIR%\main.js" "%DESKTOP_PACKAGE%\Application\" >nul 2>&1
if exist "%SOURCE_DIR%\package.json" copy "%SOURCE_DIR%\package.json" "%DESKTOP_PACKAGE%\Application\" >nul 2>&1
if exist "%SOURCE_DIR%\config.json" copy "%SOURCE_DIR%\config.json" "%DESKTOP_PACKAGE%\Application\" >nul 2>&1
if exist "index.html" copy "index.html" "%DESKTOP_PACKAGE%\Application\" >nul 2>&1

echo [3/8] Copying application folders...
if exist "%SOURCE_DIR%\assets" xcopy "%SOURCE_DIR%\assets\*" "%DESKTOP_PACKAGE%\Application\assets\" /Y /E /I >nul 2>&1
if exist "%SOURCE_DIR%\styles" xcopy "%SOURCE_DIR%\styles\*" "%DESKTOP_PACKAGE%\Application\styles\" /Y /E /I >nul 2>&1
if exist "%SOURCE_DIR%\scripts" xcopy "%SOURCE_DIR%\scripts\*" "%DESKTOP_PACKAGE%\Application\scripts\" /Y /E /I >nul 2>&1
if exist "%SOURCE_DIR%\src" xcopy "%SOURCE_DIR%\src\*" "%DESKTOP_PACKAGE%\Application\src\" /Y /E /I >nul 2>&1

echo [4/8] Copying installers...
copy "simple_installer.bat" "%DESKTOP_PACKAGE%\Installers\" >nul 2>&1
copy "manual_icon_setup.bat" "%DESKTOP_PACKAGE%\Installers\" >nul 2>&1
copy "quick_icon_fix.bat" "%DESKTOP_PACKAGE%\Installers\" >nul 2>&1
copy "install_custom_icon.bat" "%DESKTOP_PACKAGE%\Installers\" >nul 2>&1

echo [5/8] Copying tools...
copy "create_simple_icon.html" "%DESKTOP_PACKAGE%\Tools\" >nul 2>&1
copy "logo_to_icon_converter.html" "%DESKTOP_PACKAGE%\Tools\" >nul 2>&1
copy "create_temp_icon.html" "%DESKTOP_PACKAGE%\Tools\" >nul 2>&1
copy "simple_launcher.bat" "%DESKTOP_PACKAGE%\Tools\" >nul 2>&1

echo [6/8] Creating main launchers...

REM Main application launcher
(
echo @echo off
echo title Future Fuel Corporation - Gas Shop Management
echo cd /d "%%~dp0\Application"
echo echo ========================================
echo echo   Future Fuel Corporation
echo echo   Gas Shop Management System
echo echo ========================================
echo echo.
echo echo Starting application...
echo if exist "index.html" ^(
echo     start "" "index.html"
echo     echo Application started successfully!
echo ^) else ^(
echo     echo Error: Application files not found
echo ^)
echo echo.
echo pause
) > "%DESKTOP_PACKAGE%\🚀 Start Application.bat"

REM System installer launcher
(
echo @echo off
echo title Future Fuel Corporation - System Installer
echo cd /d "%%~dp0"
echo echo ========================================
echo echo   Future Fuel Corporation
echo echo   System Installer
echo echo ========================================
echo echo.
echo echo Running system installer...
echo if exist "Installers\simple_installer.bat" ^(
echo     start "" "Installers\simple_installer.bat"
echo ^) else ^(
echo     echo Error: Installer not found
echo ^)
) > "%DESKTOP_PACKAGE%\⚙️ Install to System.bat"

REM Icon setup launcher
(
echo @echo off
echo title Future Fuel Corporation - Icon Setup
echo cd /d "%%~dp0"
echo echo ========================================
echo echo   Future Fuel Corporation
echo echo   Icon Setup Tools
echo echo ========================================
echo echo.
echo echo Opening icon creation tools...
echo if exist "Tools\create_simple_icon.html" start "" "Tools\create_simple_icon.html"
echo if exist "Tools\logo_to_icon_converter.html" start "" "Tools\logo_to_icon_converter.html"
echo echo Icon tools opened!
) > "%DESKTOP_PACKAGE%\🎨 Setup Custom Icon.bat"

REM Advanced launcher
(
echo @echo off
echo title Future Fuel Corporation - Advanced Options
echo cd /d "%%~dp0"
echo echo ========================================
echo echo   Future Fuel Corporation
echo echo   Advanced Launch Options
echo echo ========================================
echo echo.
echo echo Choose an option:
echo echo [1] Run Application
echo echo [2] Install to System
echo echo [3] Setup Custom Icon
echo echo [4] Open Documentation
echo echo [5] Exit
echo echo.
echo choice /c 12345 /n /m "Choose option (1-5): "
echo if %%errorlevel%%==1 start "" "🚀 Start Application.bat"
echo if %%errorlevel%%==2 start "" "⚙️ Install to System.bat"
echo if %%errorlevel%%==3 start "" "🎨 Setup Custom Icon.bat"
echo if %%errorlevel%%==4 start "" "Documentation"
echo if %%errorlevel%%==5 exit
) > "%DESKTOP_PACKAGE%\🔧 Advanced Options.bat"

echo [7/8] Creating documentation...

REM Main README
(
echo ========================================
echo   Future Fuel Corporation
echo   Gas Shop Management System
echo   Complete Package
echo ========================================
echo.
echo CONTENTS:
echo.
echo 🚀 Start Application.bat - Run the application directly
echo ⚙️ Install to System.bat - Install to Windows system
echo 🎨 Setup Custom Icon.bat - Create custom application icon
echo 🔧 Advanced Options.bat - Advanced launch options
echo.
echo FOLDERS:
echo.
echo Application/ - Main application files
echo Installers/ - System installation tools
echo Tools/ - Icon creation and setup tools
echo Documentation/ - User guides and help files
echo.
echo QUICK START:
echo.
echo 1. Double-click "🚀 Start Application.bat" to run immediately
echo 2. Or double-click "⚙️ Install to System.bat" to install permanently
echo 3. Use "🎨 Setup Custom Icon.bat" to create a custom icon
echo.
echo SYSTEM REQUIREMENTS:
echo.
echo - Windows 7 or later
echo - Web browser (Chrome, Firefox, Edge, etc.)
echo - Optional: Node.js for advanced features
echo.
echo SUPPORT:
echo.
echo For technical support or questions, refer to the
echo documentation in the Documentation folder.
echo.
echo ========================================
echo   Future Fuel Corporation
echo   Gas Shop Management System
echo ========================================
) > "%DESKTOP_PACKAGE%\📖 READ ME FIRST.txt"

REM Installation guide
(
echo ========================================
echo   Installation Guide
echo   Future Fuel Corporation
echo ========================================
echo.
echo OPTION 1: DIRECT RUN (Recommended for testing)
echo.
echo 1. Double-click "🚀 Start Application.bat"
echo 2. The application will open in your default browser
echo 3. Start using the gas shop management system
echo.
echo OPTION 2: SYSTEM INSTALLATION (Recommended for daily use)
echo.
echo 1. Double-click "⚙️ Install to System.bat"
echo 2. Follow the installation wizard
echo 3. The application will be installed with desktop shortcuts
echo 4. Access from Start Menu or Desktop shortcut
echo.
echo OPTION 3: CUSTOM ICON SETUP
echo.
echo 1. Double-click "🎨 Setup Custom Icon.bat"
echo 2. Use the web tools to create or convert your company logo
echo 3. Follow the instructions to apply the custom icon
echo.
echo TROUBLESHOOTING:
echo.
echo - If application doesn't start, check that you have a web browser
echo - For Node.js features, install Node.js from nodejs.org
echo - For custom icons, save them as PNG or ICO format
echo.
echo ========================================
) > "%DESKTOP_PACKAGE%\Documentation\Installation Guide.txt"

echo [8/8] Finalizing package...

echo.
echo ========================================
echo   Package Creation Complete!
echo ========================================
echo.
echo Package created at: %DESKTOP_PACKAGE%
echo.
echo Package contents:
echo ✓ Complete application with all files
echo ✓ System installers and setup tools
echo ✓ Icon creation and customization tools
echo ✓ Comprehensive documentation
echo ✓ Easy-to-use launch shortcuts
echo.
echo Ready-to-use launchers:
echo 🚀 Start Application.bat - Quick start
echo ⚙️ Install to System.bat - System installation
echo 🎨 Setup Custom Icon.bat - Icon customization
echo 🔧 Advanced Options.bat - Advanced features
echo.
echo Would you like to open the package folder? (Y/N)
choice /c YN /n
if %errorlevel% equ 1 (
    echo Opening package folder...
    start "" "%DESKTOP_PACKAGE%"
)

echo.
echo Would you like to test the application? (Y/N)
choice /c YN /n
if %errorlevel% equ 1 (
    echo Testing application...
    start "" "%DESKTOP_PACKAGE%\🚀 Start Application.bat"
)

echo.
echo ========================================
echo   Package ready for distribution!
echo ========================================
echo.
echo You can now:
echo - Use the application directly from this package
echo - Share this folder with others
echo - Create a ZIP file for easy distribution
echo - Install to system for permanent use
echo.
echo Press any key to exit...
pause >nul
