@echo off
setlocal enabledelayedexpansion
title شركة وقود المستقبل - مثبت نظام إدارة محل الغاز

echo ========================================
echo      شركة وقود المستقبل
echo   نظام إدارة محل تركيب وتصليح الغاز
echo ========================================
echo.

REM Set installation directory in user folder for better compatibility
set "INSTALL_DIR=%USERPROFILE%\AppData\Local\وقود المستقبل"
set "DESKTOP_SHORTCUT=%USERPROFILE%\Desktop\شركة وقود المستقبل.lnk"
set "STARTMENU_DIR=%APPDATA%\Microsoft\Windows\Start Menu\Programs\شركة وقود المستقبل"

echo جاري التثبيت في: %INSTALL_DIR%
echo.

REM Create application directory
if not exist "%INSTALL_DIR%" (
    mkdir "%INSTALL_DIR%"
)

REM Create subdirectories
mkdir "%INSTALL_DIR%\assets" 2>nul
mkdir "%INSTALL_DIR%\styles" 2>nul
mkdir "%INSTALL_DIR%\scripts" 2>nul
mkdir "%INSTALL_DIR%\src" 2>nul

echo جاري نسخ ملفات التطبيق...

REM Copy main files
copy "index.html" "%INSTALL_DIR%\" >nul 2>&1
copy "main.js" "%INSTALL_DIR%\" >nul 2>&1
copy "config.json" "%INSTALL_DIR%\" >nul 2>&1
copy "package.json" "%INSTALL_DIR%\" >nul 2>&1

REM Copy assets
xcopy "assets\*" "%INSTALL_DIR%\assets\" /Y /E >nul 2>&1
xcopy "styles\*" "%INSTALL_DIR%\styles\" /Y /E >nul 2>&1
xcopy "scripts\*" "%INSTALL_DIR%\scripts\" /Y /E >nul 2>&1
xcopy "src\*" "%INSTALL_DIR%\src\" /Y /E >nul 2>&1

echo.
echo إنشاء ملف التشغيل الرئيسي...

REM Create main launcher that looks like a regular app
(
echo @echo off
echo title شركة وقود المستقبل - نظام إدارة محل الغاز
echo cd /d "%INSTALL_DIR%"
echo.
echo REM Hide console window for professional look
echo if not "%%1"=="hide" start /min cmd /c "%%~f0" hide ^& exit
echo.
echo REM Try Electron first, then fallback to web
echo node --version ^>nul 2^>^&1
echo if %%errorlevel%% equ 0 ^(
echo     if exist "main.js" ^(
echo         echo بدء تشغيل نظام إدارة محل الغاز...
echo         node main.js
echo     ^) else ^(
echo         start "" "index.html"
echo     ^)
echo ^) else ^(
echo     start "" "index.html"
echo ^)
) > "%INSTALL_DIR%\تشغيل التطبيق.bat"

REM Create a Windows executable-style launcher
(
echo @echo off
echo title شركة وقود المستقبل
echo cd /d "%INSTALL_DIR%"
echo start /min "" "تشغيل التطبيق.bat"
echo exit
) > "%INSTALL_DIR%\وقود المستقبل.bat"

echo.
echo إنشاء اختصار سطح المكتب...

REM Create desktop shortcut with custom icon
powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP_SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\وقود المستقبل.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.IconLocation = '%INSTALL_DIR%\assets\icon.ico'; $Shortcut.Description = 'شركة وقود المستقبل - نظام إدارة محل الغاز'; $Shortcut.WindowStyle = 7; $Shortcut.Save()}"

echo.
echo إنشاء اختصار قائمة ابدأ...

REM Create Start Menu folder and shortcut
if not exist "%STARTMENU_DIR%" (
    mkdir "%STARTMENU_DIR%"
)

powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%STARTMENU_DIR%\شركة وقود المستقبل.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\وقود المستقبل.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.IconLocation = '%INSTALL_DIR%\assets\icon.ico'; $Shortcut.Description = 'شركة وقود المستقبل - نظام إدارة محل الغاز'; $Shortcut.WindowStyle = 7; $Shortcut.Save()}"

echo.
echo إنشاء ملف إلغاء التثبيت...

REM Create uninstaller
(
echo @echo off
echo title إلغاء تثبيت شركة وقود المستقبل
echo.
echo هل أنت متأكد من إلغاء تثبيت نظام إدارة محل الغاز؟
echo اضغط Y للمتابعة أو أي مفتاح آخر للإلغاء...
echo.
echo choice /c YN /n
echo if %%errorlevel%% equ 2 goto :cancel
echo.
echo جاري إلغاء التثبيت...
echo rmdir /s /q "%INSTALL_DIR%" ^>nul 2^>^&1
echo del "%DESKTOP_SHORTCUT%" ^>nul 2^>^&1
echo rmdir /s /q "%STARTMENU_DIR%" ^>nul 2^>^&1
echo.
echo تم إلغاء التثبيت بنجاح!
echo goto :end
echo.
echo :cancel
echo تم إلغاء العملية.
echo.
echo :end
echo pause
) > "%INSTALL_DIR%\إلغاء التثبيت.bat"

echo.
echo ========================================
echo       تم التثبيت بنجاح!
echo ========================================
echo.
echo تم تثبيت التطبيق في: %INSTALL_DIR%
echo.
echo يمكنك تشغيل التطبيق من:
echo - سطح المكتب: شركة وقود المستقبل
echo - قائمة ابدأ: شركة وقود المستقبل
echo.
echo لإلغاء التثبيت: %INSTALL_DIR%\إلغاء التثبيت.bat
echo.
echo اضغط أي مفتاح للخروج...
pause >nul
