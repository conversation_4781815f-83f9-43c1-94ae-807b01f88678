@echo off
setlocal enabledelayedexpansion
title Future Fuel Corporation - Gas Shop Management

echo ========================================
echo   Future Fuel Corporation
echo   Gas Shop Management System
echo ========================================
echo.

cd /d "%~dp0"

echo Current directory: %CD%
echo.

echo Choose launch option:
echo.
echo [1] Run in Browser (Recommended)
echo [2] Run with Node.js (if installed)
echo [3] Install to System
echo [4] Setup Custom Icon
echo [5] Open User Guide
echo [6] Exit
echo.

choice /c 123456 /n /m "Choose option (1-6): "

if %errorlevel%==1 goto :web_mode
if %errorlevel%==2 goto :node_mode
if %errorlevel%==3 goto :install_mode
if %errorlevel%==4 goto :icon_mode
if %errorlevel%==5 goto :help_mode
if %errorlevel%==6 goto :exit

:web_mode
echo.
echo [WEB] Running application in browser...
echo.
if exist "index.html" (
    echo ✓ Application file found
    echo ✓ Opening in browser...
    start "" "index.html"
    echo.
    echo ✅ Application started successfully!
    echo 🌐 Check your browser window
) else (
    echo ❌ Error: index.html not found
)
goto :end

:node_mode
echo.
echo [NODE] Running with Node.js...
echo.
node --version >nul 2>&1
if %errorlevel%==0 (
    echo ✓ Node.js found
    if exist "main.js" (
        echo ✓ Starting local server...
        echo.
        echo 🚀 Application running on: http://localhost:3000
        echo 📝 Press Ctrl+C to stop server
        echo.
        node main.js
    ) else (
        echo ❌ main.js not found
        echo 🔄 Switching to web mode...
        goto :web_mode
    )
) else (
    echo ❌ Node.js not installed
    echo 🔄 Switching to web mode...
    goto :web_mode
)
goto :end

:install_mode
echo.
echo [INSTALL] Installing to system...
echo.
if exist "installers\simple_installer.bat" (
    echo ✓ Running installer...
    start "" "installers\simple_installer.bat"
    echo ✅ Installer started
) else (
    echo ❌ Installer not found
)
goto :end

:icon_mode
echo.
echo [ICON] Setting up custom icon...
echo.
if exist "tools\create_simple_icon.html" (
    echo ✓ Opening icon creator...
    start "" "tools\create_simple_icon.html"
)
if exist "tools\logo_to_icon_converter.html" (
    echo ✓ Opening logo converter...
    start "" "tools\logo_to_icon_converter.html"
)
echo ✅ Icon tools opened
goto :end

:help_mode
echo.
echo [HELP] Opening user guide...
echo.
if exist "README.txt" (
    echo ✓ Opening user guide...
    start "" "README.txt"
    echo ✅ Guide opened
) else (
    echo ❌ User guide not found
)
goto :end

:exit
echo.
echo 👋 Thank you for using Future Fuel Corporation system
goto :end

:end
echo.
echo ========================================
echo   For help, check README.txt file
echo ========================================
echo.
echo Press any key to exit...
pause >nul
