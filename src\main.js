const { app, BrowserWindow, Menu, dialog, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');

// تعطيل تحذيرات الأمان في بيئة التطوير
process.env.NODE_ENV = 'production';

// المتغير العام للنافذة الرئيسية
let mainWindow;

// إنشاء النافذة الرئيسية
function createMainWindow() {
  mainWindow = new BrowserWindow({
    title: 'نظام إدارة محل تركيب وتصليح الغاز للسيارات',
    width: 1200,
    height: 800,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false
    },
    icon: path.join(__dirname, '../assets/icon.ico')
  });

  // تحميل الصفحة الرئيسية
  mainWindow.loadFile(path.join(__dirname, '../index.html'));

  // فتح أدوات المطور في بيئة التطوير
  // mainWindow.webContents.openDevTools();

  // إنشاء قائمة التطبيق
  const mainMenu = Menu.buildFromTemplate(menuTemplate);
  Menu.setApplicationMenu(mainMenu);

  // إغلاق النافذة
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// إنشاء قائمة التطبيق
const menuTemplate = [
  {
    label: 'ملف',
    submenu: [
      {
        label: 'حفظ البيانات',
        accelerator: process.platform === 'darwin' ? 'Command+S' : 'Ctrl+S',
        click() {
          mainWindow.webContents.executeJavaScript('saveData()');
        }
      },
      {
        label: 'طباعة',
        accelerator: process.platform === 'darwin' ? 'Command+P' : 'Ctrl+P',
        click() {
          mainWindow.webContents.print();
        }
      },
      {
        type: 'separator'
      },
      {
        label: 'خروج',
        accelerator: process.platform === 'darwin' ? 'Command+Q' : 'Ctrl+Q',
        click() {
          app.quit();
        }
      }
    ]
  },
  {
    label: 'عرض',
    submenu: [
      {
        label: 'تحديث',
        accelerator: 'F5',
        click() {
          mainWindow.reload();
        }
      },
      {
        label: 'تكبير',
        accelerator: process.platform === 'darwin' ? 'Command+Plus' : 'Ctrl+Plus',
        role: 'zoomIn'
      },
      {
        label: 'تصغير',
        accelerator: process.platform === 'darwin' ? 'Command+-' : 'Ctrl+-',
        role: 'zoomOut'
      },
      {
        label: 'ملء الشاشة',
        accelerator: 'F11',
        click() {
          mainWindow.setFullScreen(!mainWindow.isFullScreen());
        }
      }
    ]
  },
  {
    label: 'مساعدة',
    submenu: [
      {
        label: 'حول البرنامج',
        click() {
          dialog.showMessageBox(mainWindow, {
            type: 'info',
            title: 'حول البرنامج',
            message: 'نظام إدارة محل تركيب وتصليح الغاز للسيارات',
            detail: 'الإصدار 1.0.0\nجميع الحقوق محفوظة © 2023',
            buttons: ['موافق']
          });
        }
      }
    ]
  }
];

// إضافة أدوات المطور في بيئة التطوير
if (process.env.NODE_ENV !== 'production') {
  menuTemplate.push({
    label: 'أدوات المطور',
    submenu: [
      { role: 'reload' },
      { role: 'forcereload' },
      { type: 'separator' },
      { role: 'toggledevtools' }
    ]
  });
}

// معالجة أحداث IPC
const setupIpcHandlers = () => {
  // فتح مربع حوار حفظ الملف
  ipcMain.handle('save-dialog', async (event, options) => {
    const { canceled, filePath } = await dialog.showSaveDialog(options);
    if (canceled) {
      return null;
    }
    return filePath;
  });

  // فتح مربع حوار فتح الملف
  ipcMain.handle('open-dialog', async (event, options) => {
    const { canceled, filePaths } = await dialog.showOpenDialog(options);
    if (canceled) {
      return null;
    }
    return filePaths[0];
  });

  // إنشاء نسخة احتياطية تلقائية
  ipcMain.on('create-auto-backup', (event) => {
    // يمكن إضافة كود لإنشاء نسخة احتياطية تلقائية هنا
  });
};

// بدء التطبيق
app.whenReady().then(() => {
  createMainWindow();
  setupIpcHandlers();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createMainWindow();
    }
  });
});

// إغلاق التطبيق
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});