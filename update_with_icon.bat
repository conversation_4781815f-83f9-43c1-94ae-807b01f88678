@echo off
setlocal enabledelayedexpansion
title Gas Shop Management System - Icon Update

echo ========================================
echo   Gas Shop Management System
echo   Icon Update Utility
echo ========================================
echo.

set "INSTALL_DIR=%USERPROFILE%\AppData\Local\GasShopManagement"
set "DESKTOP_SHORTCUT=%USERPROFILE%\Desktop\Gas Shop Management.lnk"
set "STARTMENU_DIR=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Gas Shop Management"

echo Checking installation...
if not exist "%INSTALL_DIR%" (
    echo Error: Application not found. Please run the installer first.
    pause
    exit /b 1
)

echo Creating custom icon...

REM Create a better icon using PowerShell
powershell -Command "& {
    Add-Type -AssemblyName System.Drawing
    $bmp = New-Object System.Drawing.Bitmap(64,64)
    $g = [System.Drawing.Graphics]::FromImage($bmp)
    $g.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
    
    # Background gradient
    $brush = New-Object System.Drawing.Drawing2D.LinearGradientBrush([System.Drawing.Point]::new(0,0), [System.Drawing.Point]::new(64,64), [System.Drawing.Color]::FromArgb(76,175,80), [System.Drawing.Color]::FromArgb(46,125,50))
    $g.FillRectangle($brush, 0, 0, 64, 64)
    
    # Border
    $pen = New-Object System.Drawing.Pen([System.Drawing.Color]::FromArgb(27,94,32), 2)
    $g.DrawRectangle($pen, 1, 1, 62, 62)
    
    # Car shape
    $carBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
    $g.FillEllipse($carBrush, 12, 35, 40, 15)
    $g.FillEllipse($carBrush, 18, 28, 28, 12)
    
    # Wheels
    $wheelBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::Black)
    $g.FillEllipse($wheelBrush, 18, 45, 8, 8)
    $g.FillEllipse($wheelBrush, 38, 45, 8, 8)
    
    # Gas pump
    $g.FillRectangle($carBrush, 48, 25, 12, 8)
    $g.FillRectangle($carBrush, 58, 22, 6, 14)
    
    # Text
    $font = New-Object System.Drawing.Font('Arial', 8, [System.Drawing.FontStyle]::Bold)
    $textBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
    $g.DrawString('GAS', $font, $textBrush, 22, 8)
    
    $g.Dispose()
    $bmp.Save('%INSTALL_DIR%\assets\icon.png', [System.Drawing.Imaging.ImageFormat]::Png)
    $bmp.Dispose()
    $brush.Dispose()
    $pen.Dispose()
    $carBrush.Dispose()
    $wheelBrush.Dispose()
    $textBrush.Dispose()
    $font.Dispose()
}" 2>nul

echo Updating desktop shortcut...
REM Remove old shortcut
del "%DESKTOP_SHORTCUT%" >nul 2>&1

REM Create new shortcut with icon
powershell -Command "& {
    $WshShell = New-Object -comObject WScript.Shell
    $Shortcut = $WshShell.CreateShortcut('%DESKTOP_SHORTCUT%')
    $Shortcut.TargetPath = '%INSTALL_DIR%\launch_silent.vbs'
    $Shortcut.WorkingDirectory = '%INSTALL_DIR%'
    $Shortcut.IconLocation = '%INSTALL_DIR%\assets\icon.png'
    $Shortcut.Description = 'Gas Shop Management System - Future Fuel Corporation'
    $Shortcut.Save()
}" 2>nul

echo Updating Start Menu shortcut...
REM Remove old shortcut
rmdir /s /q "%STARTMENU_DIR%" >nul 2>&1

REM Create new Start Menu shortcut
if not exist "%STARTMENU_DIR%" mkdir "%STARTMENU_DIR%"
powershell -Command "& {
    $WshShell = New-Object -comObject WScript.Shell
    $Shortcut = $WshShell.CreateShortcut('%STARTMENU_DIR%\Gas Shop Management.lnk')
    $Shortcut.TargetPath = '%INSTALL_DIR%\launch_silent.vbs'
    $Shortcut.WorkingDirectory = '%INSTALL_DIR%'
    $Shortcut.IconLocation = '%INSTALL_DIR%\assets\icon.png'
    $Shortcut.Description = 'Gas Shop Management System - Future Fuel Corporation'
    $Shortcut.Save()
}" 2>nul

echo Creating additional shortcuts...

REM Create a direct web launcher shortcut
powershell -Command "& {
    $WshShell = New-Object -comObject WScript.Shell
    $Shortcut = $WshShell.CreateShortcut('%STARTMENU_DIR%\Gas Shop Management (Web).lnk')
    $Shortcut.TargetPath = '%INSTALL_DIR%\index.html'
    $Shortcut.WorkingDirectory = '%INSTALL_DIR%'
    $Shortcut.IconLocation = '%INSTALL_DIR%\assets\icon.png'
    $Shortcut.Description = 'Gas Shop Management System - Web Version'
    $Shortcut.Save()
}" 2>nul

echo.
echo ========================================
echo   Icon update completed successfully!
echo ========================================
echo.
echo Updated shortcuts:
echo ✓ Desktop: Gas Shop Management (with custom icon)
echo ✓ Start Menu: Gas Shop Management (with custom icon)
echo ✓ Start Menu: Gas Shop Management (Web) (direct web access)
echo.
echo The application now has a professional appearance!
echo.
echo Press any key to exit...
pause >nul
