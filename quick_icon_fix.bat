@echo off
setlocal enabledelayedexpansion
title Quick Icon Fix - Future Fuel Corporation

echo ========================================
echo   Quick Icon Fix
echo   Future Fuel Corporation
echo ========================================
echo.

set "INSTALL_DIR=%USERPROFILE%\AppData\Local\GasShopManagement"
set "DESKTOP_SHORTCUT=%USERPROFILE%\Desktop\Gas Shop Management.lnk"
set "STARTMENU_DIR=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Gas Shop Management"

echo Checking installation...
if not exist "%INSTALL_DIR%" (
    echo Error: Application not found.
    pause
    exit /b 1
)

echo Creating a simple icon using PowerShell...

REM Create a simple green icon
powershell -Command "& {
    Add-Type -AssemblyName System.Drawing
    $bmp = New-Object System.Drawing.Bitmap(64,64)
    $g = [System.Drawing.Graphics]::FromImage($bmp)
    $g.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
    
    # Green background
    $brush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(76,175,80))
    $g.FillRectangle($brush, 0, 0, 64, 64)
    
    # White car shape
    $carBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
    $g.FillEllipse($carBrush, 8, 25, 48, 20)
    $g.FillEllipse($carBrush, 16, 18, 32, 14)
    
    # Black wheels
    $wheelBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::Black)
    $g.FillEllipse($wheelBrush, 14, 38, 10, 10)
    $g.FillEllipse($wheelBrush, 40, 38, 10, 10)
    
    # Gas pump
    $g.FillRectangle($carBrush, 52, 20, 8, 6)
    
    $g.Dispose()
    $bmp.Save('%INSTALL_DIR%\assets\icon.png', [System.Drawing.Imaging.ImageFormat]::Png)
    $bmp.Dispose()
    $brush.Dispose()
    $carBrush.Dispose()
    $wheelBrush.Dispose()
}" 2>nul

echo Creating ICO version...
copy "%INSTALL_DIR%\assets\icon.png" "%INSTALL_DIR%\assets\icon.ico" >nul 2>&1

echo Updating desktop shortcut...
del "%DESKTOP_SHORTCUT%" >nul 2>&1

powershell -Command "& {
    $WshShell = New-Object -comObject WScript.Shell
    $Shortcut = $WshShell.CreateShortcut('%DESKTOP_SHORTCUT%')
    $Shortcut.TargetPath = '%INSTALL_DIR%\launch_silent.vbs'
    $Shortcut.WorkingDirectory = '%INSTALL_DIR%'
    $Shortcut.IconLocation = '%INSTALL_DIR%\assets\icon.ico'
    $Shortcut.Description = 'Future Fuel Corporation - Gas Shop Management'
    $Shortcut.Save()
}" 2>nul

echo Updating Start Menu shortcuts...
rmdir /s /q "%STARTMENU_DIR%" >nul 2>&1
if not exist "%STARTMENU_DIR%" mkdir "%STARTMENU_DIR%"

powershell -Command "& {
    $WshShell = New-Object -comObject WScript.Shell
    $Shortcut = $WshShell.CreateShortcut('%STARTMENU_DIR%\Future Fuel Corporation.lnk')
    $Shortcut.TargetPath = '%INSTALL_DIR%\launch_silent.vbs'
    $Shortcut.WorkingDirectory = '%INSTALL_DIR%'
    $Shortcut.IconLocation = '%INSTALL_DIR%\assets\icon.ico'
    $Shortcut.Description = 'Future Fuel Corporation - Gas Shop Management'
    $Shortcut.Save()
}" 2>nul

powershell -Command "& {
    $WshShell = New-Object -comObject WScript.Shell
    $Shortcut = $WshShell.CreateShortcut('%STARTMENU_DIR%\Future Fuel (Web).lnk')
    $Shortcut.TargetPath = '%INSTALL_DIR%\index.html'
    $Shortcut.WorkingDirectory = '%INSTALL_DIR%'
    $Shortcut.IconLocation = '%INSTALL_DIR%\assets\icon.ico'
    $Shortcut.Description = 'Future Fuel Corporation - Web Version'
    $Shortcut.Save()
}" 2>nul

echo.
echo ========================================
echo   Icon update completed successfully!
echo ========================================
echo.
echo Updated shortcuts:
echo ✓ Desktop: Gas Shop Management (with custom icon)
echo ✓ Start Menu: Future Fuel Corporation (with custom icon)
echo ✓ Start Menu: Future Fuel (Web)
echo.
echo The application now has a custom green icon!
echo.
echo Would you like to test the application? (Y/N)
choice /c YN /n
if %errorlevel% equ 1 (
    echo Starting application...
    start "" "%INSTALL_DIR%\launch_silent.vbs"
    echo.
    echo Check your desktop for the updated shortcut!
)

echo.
echo Press any key to exit...
pause >nul
