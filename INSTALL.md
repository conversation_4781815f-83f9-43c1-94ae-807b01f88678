# تعليمات تثبيت نظام إدارة محل تركيب وتصليح الغاز للسيارات

## متطلبات النظام

- نظام تشغيل Windows 10 أو أحدث
- مساحة تخزين 100 ميجابايت على الأقل
- Node.js (الإصدار 14 أو أحدث) - مطلوب للتطوير فقط

## خطوات التثبيت

### 1. تثبيت البرنامج من ملف التثبيت

إذا كان لديك ملف التثبيت (setup.exe):

1. قم بتشغيل ملف التثبيت بالنقر المزدوج عليه
2. اتبع التعليمات التي تظهر على الشاشة
3. اختر مكان التثبيت المناسب (المكان الافتراضي هو Program Files)
4. انتظر حتى اكتمال عملية التثبيت
5. انقر على "إنهاء" لإكمال عملية التثبيت
6. يمكنك الآن تشغيل البرنامج من سطح المكتب أو قائمة ابدأ

### 2. إنشاء ملف التثبيت بنفسك

إذا كنت ترغب في إنشاء ملف التثبيت بنفسك:

1. تأكد من تثبيت Node.js على جهازك
   - يمكنك تنزيله من [الموقع الرسمي](https://nodejs.org/)
   - تأكد من اختيار الإصدار LTS للحصول على أفضل استقرار

2. افتح موجه الأوامر (Command Prompt) بصلاحيات المسؤول
   - اضغط على زر Windows + X
   - اختر "موجه الأوامر (مسؤول)" أو "Windows PowerShell (مسؤول)"

3. انتقل إلى مجلد المشروع
   ```
   cd مسار_المجلد
   ```

4. قم بتثبيت الاعتمادات
   ```
   npm install
   ```

5. قم بإنشاء ملف التثبيت
   ```
   npm run dist
   ```

6. انتظر حتى اكتمال العملية. سيتم إنشاء ملف التثبيت في مجلد `dist`

7. يمكنك الآن تشغيل ملف التثبيت واتباع الخطوات في القسم السابق

### 3. تشغيل البرنامج في وضع التطوير

إذا كنت مطوراً وترغب في تشغيل البرنامج في وضع التطوير:

1. اتبع الخطوات من 1 إلى 4 في القسم السابق

2. قم بتشغيل البرنامج
   ```
   npm start
   ```

3. سيتم فتح البرنامج في نافذة جديدة

## إنشاء أيقونة التطبيق

قبل إنشاء ملف التثبيت، يجب إنشاء أيقونة للتطبيق:

1. افتح ملف `create-icon.html` في متصفح الويب
2. انقر على زر "إنشاء أيقونة" لإنشاء أيقونة افتراضية
3. يمكنك تعديل الكود في الملف لتغيير شكل الأيقونة حسب رغبتك
4. انقر على زر "تنزيل الأيقونة" لتنزيل الأيقونة بصيغة PNG
5. استخدم أداة تحويل مثل [icoconvert.com](https://icoconvert.com/) لتحويل الصورة إلى صيغة ICO
6. ضع ملف `icon.ico` في مجلد المشروع

## استكشاف الأخطاء وإصلاحها

### البرنامج لا يعمل بعد التثبيت

- تأكد من تثبيت آخر تحديثات Windows
- تأكد من وجود مساحة كافية على القرص الصلب
- جرب تشغيل البرنامج كمسؤول (انقر بزر الماوس الأيمن واختر "تشغيل كمسؤول")

### خطأ أثناء إنشاء ملف التثبيت

- تأكد من تثبيت Node.js بشكل صحيح
- تأكد من تثبيت جميع الاعتمادات بشكل صحيح
- جرب حذف مجلد `node_modules` وتشغيل `npm install` مرة أخرى

## الدعم الفني

للحصول على المساعدة، يرجى التواصل عبر:
- البريد الإلكتروني: <EMAIL>
- الهاتف: 0123456789
