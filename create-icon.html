<!DOCTYPE html>
<html>
<head>
    <title>إنشاء أيقونة التطبيق</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            margin: 20px;
            direction: rtl;
        }
        canvas {
            border: 1px solid #ccc;
            margin: 20px auto;
            display: block;
        }
        .controls {
            margin: 20px;
        }
        button {
            padding: 10px 20px;
            margin: 10px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>إنشاء أيقونة التطبيق</h1>
    <p>استخدم هذه الصفحة لإنشاء أيقونة بسيطة للتطبيق</p>
    
    <canvas id="iconCanvas" width="256" height="256"></canvas>
    
    <div class="controls">
        <button id="generateBtn">إنشاء أيقونة</button>
        <button id="downloadBtn">تنزيل الأيقونة</button>
    </div>
    
    <script>
        const canvas = document.getElementById('iconCanvas');
        const ctx = canvas.getContext('2d');
        
        // إنشاء أيقونة بسيطة
        function generateIcon() {
            // تنظيف الكانفاس
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // خلفية الأيقونة
            ctx.fillStyle = '#2c3e50';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // رسم دائرة
            ctx.fillStyle = '#3498db';
            ctx.beginPath();
            ctx.arc(canvas.width/2, canvas.height/2, 100, 0, Math.PI * 2);
            ctx.fill();
            
            // رسم رمز الغاز
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 120px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('G', canvas.width/2, canvas.height/2);
            
            // إضافة حدود
            ctx.strokeStyle = '#ecf0f1';
            ctx.lineWidth = 10;
            ctx.strokeRect(10, 10, canvas.width - 20, canvas.height - 20);
        }
        
        // تنزيل الأيقونة
        function downloadIcon() {
            const link = document.createElement('a');
            link.download = 'icon.png';
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        // إضافة مستمعي الأحداث للأزرار
        document.getElementById('generateBtn').addEventListener('click', generateIcon);
        document.getElementById('downloadBtn').addEventListener('click', downloadIcon);
        
        // إنشاء أيقونة افتراضية عند تحميل الصفحة
        window.onload = generateIcon;
    </script>
</body>
</html>
