<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء أيقونة مؤقتة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        canvas {
            border: 2px solid #ddd;
            border-radius: 10px;
            margin: 20px;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>إنشاء أيقونة مؤقتة لشركة وقود المستقبل</h1>
        <canvas id="iconCanvas" width="256" height="256"></canvas>
        <br>
        <button onclick="createIcon()">إنشاء أيقونة</button>
        <button onclick="downloadIcon()">تنزيل PNG</button>
        <button onclick="downloadICO()">تنزيل ICO</button>
        <button onclick="saveToApp()">حفظ في التطبيق</button>
    </div>

    <script>
        function createIcon() {
            const canvas = document.getElementById('iconCanvas');
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, 256, 256);
            
            // Background gradient
            const gradient = ctx.createRadialGradient(128, 128, 0, 128, 128, 180);
            gradient.addColorStop(0, '#4CAF50');
            gradient.addColorStop(0.7, '#2E7D32');
            gradient.addColorStop(1, '#1B5E20');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 256, 256);
            
            // Border
            ctx.strokeStyle = '#0D4E12';
            ctx.lineWidth = 8;
            ctx.strokeRect(4, 4, 248, 248);
            
            // Car silhouette (simplified)
            ctx.fillStyle = '#FFFFFF';
            ctx.shadowColor = 'rgba(0,0,0,0.3)';
            ctx.shadowBlur = 10;
            ctx.shadowOffsetX = 2;
            ctx.shadowOffsetY = 2;
            
            // Car body
            ctx.beginPath();
            ctx.ellipse(128, 140, 80, 30, 0, 0, 2 * Math.PI);
            ctx.fill();
            
            // Car top
            ctx.beginPath();
            ctx.ellipse(128, 120, 50, 20, 0, 0, 2 * Math.PI);
            ctx.fill();
            
            // Wheels
            ctx.fillStyle = '#333333';
            ctx.beginPath();
            ctx.arc(90, 155, 15, 0, 2 * Math.PI);
            ctx.fill();
            ctx.beginPath();
            ctx.arc(166, 155, 15, 0, 2 * Math.PI);
            ctx.fill();
            
            // Gas pump nozzle
            ctx.fillStyle = '#FFFFFF';
            ctx.fillRect(180, 100, 40, 15);
            ctx.fillRect(220, 95, 20, 25);
            
            // Reset shadow
            ctx.shadowColor = 'transparent';
            ctx.shadowBlur = 0;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 0;
            
            // Arabic text
            ctx.fillStyle = '#FFFFFF';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.strokeStyle = '#1B5E20';
            ctx.lineWidth = 2;
            ctx.strokeText('وقود', 128, 50);
            ctx.fillText('وقود', 128, 50);
            
            ctx.font = 'bold 16px Arial';
            ctx.strokeText('المستقبل', 128, 30);
            ctx.fillText('المستقبل', 128, 30);
            
            // Add "GAS" text
            ctx.fillStyle = '#81C784';
            ctx.font = 'bold 20px Arial';
            ctx.fillText('GAS', 128, 200);
        }
        
        function downloadIcon() {
            const canvas = document.getElementById('iconCanvas');
            const link = document.createElement('a');
            link.download = 'gas-shop-icon.png';
            link.href = canvas.toDataURL();
            link.click();
        }
        
        function downloadICO() {
            const canvas = document.getElementById('iconCanvas');
            const smallCanvas = document.createElement('canvas');
            smallCanvas.width = 32;
            smallCanvas.height = 32;
            const ctx = smallCanvas.getContext('2d');
            ctx.drawImage(canvas, 0, 0, 32, 32);
            
            const link = document.createElement('a');
            link.download = 'gas-shop-icon.ico';
            link.href = smallCanvas.toDataURL();
            link.click();
        }
        
        function saveToApp() {
            downloadIcon();
            downloadICO();
            alert('تم تنزيل الأيقونات!\n\nالآن:\n1. انسخ الملفات إلى مجلد assets\n2. أعد تشغيل المثبت لتحديث الاختصارات');
        }
        
        // Create icon on page load
        window.onload = createIcon;
    </script>
</body>
</html>
