<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحويل الصورة إلى أيقونة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 600px;
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
        }
        .upload-area {
            border: 3px dashed #4CAF50;
            border-radius: 10px;
            padding: 40px;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            background-color: #f5f5f5;
            border-color: #2E7D32;
        }
        .upload-area.dragover {
            background-color: #e8f5e8;
            border-color: #1B5E20;
        }
        input[type="file"] {
            display: none;
        }
        button {
            background: linear-gradient(45deg, #4CAF50, #2E7D32);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .preview {
            margin: 20px 0;
            display: none;
        }
        .preview img {
            max-width: 200px;
            max-height: 200px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .icon-sizes {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .icon-size {
            text-align: center;
        }
        .icon-size canvas {
            border: 1px solid #ddd;
            border-radius: 5px;
            margin: 5px;
        }
        .download-section {
            margin-top: 30px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>تحويل شعار الشركة إلى أيقونة التطبيق</h1>
        
        <div class="upload-area" onclick="document.getElementById('fileInput').click()">
            <p>اسحب وأفلت صورة الشعار هنا أو انقر للاختيار</p>
            <p style="font-size: 14px; color: #666;">يدعم: PNG, JPG, GIF</p>
        </div>
        
        <input type="file" id="fileInput" accept="image/*">
        
        <div class="preview" id="preview">
            <h3>معاينة الصورة الأصلية:</h3>
            <img id="originalImage" alt="الصورة الأصلية">
        </div>
        
        <div class="download-section" id="downloadSection">
            <h3>أحجام الأيقونات المُنشأة:</h3>
            <div class="icon-sizes" id="iconSizes"></div>
            
            <div style="margin-top: 20px;">
                <button onclick="downloadAllIcons()">تنزيل جميع الأحجام</button>
                <button onclick="downloadMainIcon()">تنزيل الأيقونة الرئيسية</button>
                <button onclick="copyToAssets()">نسخ إلى مجلد Assets</button>
            </div>
        </div>
    </div>

    <script>
        let originalImage = null;
        const iconSizes = [16, 24, 32, 48, 64, 128, 256];
        
        // File input handling
        document.getElementById('fileInput').addEventListener('change', handleFileSelect);
        
        // Drag and drop handling
        const uploadArea = document.querySelector('.upload-area');
        uploadArea.addEventListener('dragover', handleDragOver);
        uploadArea.addEventListener('dragleave', handleDragLeave);
        uploadArea.addEventListener('drop', handleDrop);
        
        function handleDragOver(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        }
        
        function handleDragLeave(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        }
        
        function handleDrop(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                processFile(files[0]);
            }
        }
        
        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                processFile(file);
            }
        }
        
        function processFile(file) {
            if (!file.type.startsWith('image/')) {
                alert('يرجى اختيار ملف صورة صالح');
                return;
            }
            
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = new Image();
                img.onload = function() {
                    originalImage = img;
                    showPreview(e.target.result);
                    generateIcons();
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
        
        function showPreview(src) {
            const preview = document.getElementById('preview');
            const originalImg = document.getElementById('originalImage');
            originalImg.src = src;
            preview.style.display = 'block';
        }
        
        function generateIcons() {
            const iconSizesContainer = document.getElementById('iconSizes');
            iconSizesContainer.innerHTML = '';
            
            iconSizes.forEach(size => {
                const canvas = document.createElement('canvas');
                canvas.width = size;
                canvas.height = size;
                canvas.id = `icon-${size}`;
                
                const ctx = canvas.getContext('2d');
                
                // Create a square crop of the original image
                const minDimension = Math.min(originalImage.width, originalImage.height);
                const sx = (originalImage.width - minDimension) / 2;
                const sy = (originalImage.height - minDimension) / 2;
                
                // Draw the image to fit the canvas
                ctx.drawImage(originalImage, sx, sy, minDimension, minDimension, 0, 0, size, size);
                
                // Create container for this icon size
                const iconDiv = document.createElement('div');
                iconDiv.className = 'icon-size';
                iconDiv.innerHTML = `<div>${size}x${size}</div>`;
                iconDiv.appendChild(canvas);
                
                iconSizesContainer.appendChild(iconDiv);
            });
            
            document.getElementById('downloadSection').style.display = 'block';
        }
        
        function downloadAllIcons() {
            iconSizes.forEach(size => {
                const canvas = document.getElementById(`icon-${size}`);
                const link = document.createElement('a');
                link.download = `gas-shop-icon-${size}x${size}.png`;
                link.href = canvas.toDataURL();
                link.click();
            });
        }
        
        function downloadMainIcon() {
            const canvas = document.getElementById('icon-256');
            const link = document.createElement('a');
            link.download = 'gas-shop-icon.png';
            link.href = canvas.toDataURL();
            link.click();
            
            // Also download the 32x32 version for ICO
            const canvas32 = document.getElementById('icon-32');
            const link32 = document.createElement('a');
            link32.download = 'gas-shop-icon.ico';
            link32.href = canvas32.toDataURL();
            link32.click();
        }
        
        function copyToAssets() {
            alert('سيتم تنزيل الأيقونات. يرجى نسخها يدوياً إلى مجلد assets في مشروع التطبيق.');
            downloadMainIcon();
        }
    </script>
</body>
</html>
