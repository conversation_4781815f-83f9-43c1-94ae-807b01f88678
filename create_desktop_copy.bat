@echo off
setlocal enabledelayedexpansion
title إنشاء نسخة على سطح المكتب - شركة وقود المستقبل

echo ========================================
echo   إنشاء نسخة على سطح المكتب
echo   شركة وقود المستقبل
echo ========================================
echo.

set "SOURCE_DIR=%USERPROFILE%\AppData\Local\GasShopManagement"
set "DESKTOP_COPY=%USERPROFILE%\Desktop\شركة وقود المستقبل - نسخة كاملة"
set "CURRENT_DIR=%CD%"

echo المصدر: %SOURCE_DIR%
echo الهدف: %DESKTOP_COPY%
echo.

echo [1/5] إنشاء مجلد النسخة على سطح المكتب...
if exist "%DESKTOP_COPY%" rmdir /s /q "%DESKTOP_COPY%"
mkdir "%DESKTOP_COPY%"
mkdir "%DESKTOP_COPY%\assets"
mkdir "%DESKTOP_COPY%\styles"
mkdir "%DESKTOP_COPY%\scripts"
mkdir "%DESKTOP_COPY%\src"
mkdir "%DESKTOP_COPY%\installers"
mkdir "%DESKTOP_COPY%\tools"

echo [2/5] نسخ ملفات التطبيق الأساسية...
if exist "%SOURCE_DIR%\index.html" copy "%SOURCE_DIR%\index.html" "%DESKTOP_COPY%\" >nul 2>&1
if exist "%SOURCE_DIR%\main.js" copy "%SOURCE_DIR%\main.js" "%DESKTOP_COPY%\" >nul 2>&1
if exist "%SOURCE_DIR%\package.json" copy "%SOURCE_DIR%\package.json" "%DESKTOP_COPY%\" >nul 2>&1
if exist "%SOURCE_DIR%\config.json" copy "%SOURCE_DIR%\config.json" "%DESKTOP_COPY%\" >nul 2>&1

echo [3/5] نسخ المجلدات...
if exist "%SOURCE_DIR%\assets" xcopy "%SOURCE_DIR%\assets\*" "%DESKTOP_COPY%\assets\" /Y /E /I >nul 2>&1
if exist "%SOURCE_DIR%\styles" xcopy "%SOURCE_DIR%\styles\*" "%DESKTOP_COPY%\styles\" /Y /E /I >nul 2>&1
if exist "%SOURCE_DIR%\scripts" xcopy "%SOURCE_DIR%\scripts\*" "%DESKTOP_COPY%\scripts\" /Y /E /I >nul 2>&1
if exist "%SOURCE_DIR%\src" xcopy "%SOURCE_DIR%\src\*" "%DESKTOP_COPY%\src\" /Y /E /I >nul 2>&1

echo [4/5] نسخ أدوات التثبيت والإعداد...
copy "simple_installer.bat" "%DESKTOP_COPY%\installers\" >nul 2>&1
copy "manual_icon_setup.bat" "%DESKTOP_COPY%\installers\" >nul 2>&1
copy "quick_icon_fix.bat" "%DESKTOP_COPY%\installers\" >nul 2>&1
copy "create_simple_icon.html" "%DESKTOP_COPY%\tools\" >nul 2>&1
copy "logo_to_icon_converter.html" "%DESKTOP_COPY%\tools\" >nul 2>&1
copy "create_temp_icon.html" "%DESKTOP_COPY%\tools\" >nul 2>&1

echo [5/5] إنشاء ملفات التشغيل والإعداد...

REM Create main launcher
(
echo @echo off
echo title شركة وقود المستقبل - نظام إدارة محل الغاز
echo cd /d "%%~dp0"
echo.
echo echo ========================================
echo echo   شركة وقود المستقبل
echo echo   نظام إدارة محل الغاز
echo echo ========================================
echo echo.
echo echo تشغيل التطبيق...
echo start "" "index.html"
) > "%DESKTOP_COPY%\تشغيل التطبيق.bat"

REM Create installer
(
echo @echo off
echo title تثبيت شركة وقود المستقبل
echo cd /d "%%~dp0"
echo echo ========================================
echo echo   تثبيت شركة وقود المستقبل
echo echo ========================================
echo echo.
echo echo تشغيل المثبت...
echo start "" "installers\simple_installer.bat"
) > "%DESKTOP_COPY%\تثبيت التطبيق.bat"

REM Create icon setup
(
echo @echo off
echo title إعداد الأيقونة
echo cd /d "%%~dp0"
echo echo ========================================
echo echo   إعداد أيقونة التطبيق
echo echo ========================================
echo echo.
echo echo فتح أداة إنشاء الأيقونة...
echo start "" "tools\create_simple_icon.html"
echo echo.
echo echo فتح أداة تحويل الشعار...
echo start "" "tools\logo_to_icon_converter.html"
) > "%DESKTOP_COPY%\إعداد الأيقونة.bat"

REM Create README file
(
echo ========================================
echo   شركة وقود المستقبل
echo   نظام إدارة محل الغاز
echo ========================================
echo.
echo محتويات المجلد:
echo.
echo الملفات الرئيسية:
echo - index.html: الملف الرئيسي للتطبيق
echo - main.js: ملف JavaScript الرئيسي
echo - package.json: إعدادات المشروع
echo - config.json: إعدادات التطبيق
echo.
echo ملفات التشغيل:
echo - تشغيل التطبيق.bat: تشغيل التطبيق مباشرة
echo - تثبيت التطبيق.bat: تثبيت التطبيق في النظام
echo - إعداد الأيقونة.bat: إنشاء أيقونة مخصصة
echo.
echo المجلدات:
echo - assets/: الصور والأيقونات
echo - styles/: ملفات CSS
echo - scripts/: ملفات JavaScript
echo - src/: الكود المصدري
echo - installers/: أدوات التثبيت
echo - tools/: أدوات مساعدة
echo.
echo طريقة الاستخدام:
echo 1. للتشغيل المباشر: انقر على "تشغيل التطبيق.bat"
echo 2. للتثبيت في النظام: انقر على "تثبيت التطبيق.bat"
echo 3. لإنشاء أيقونة: انقر على "إعداد الأيقونة.bat"
echo.
echo ========================================
echo   Future Fuel Corporation
echo   Gas Shop Management System
echo ========================================
) > "%DESKTOP_COPY%\اقرأني - README.txt"

echo.
echo ========================================
echo   تم إنشاء النسخة بنجاح!
echo ========================================
echo.
echo تم إنشاء نسخة كاملة في:
echo %DESKTOP_COPY%
echo.
echo محتويات النسخة:
echo ✓ جميع ملفات التطبيق
echo ✓ أدوات التثبيت
echo ✓ أدوات إنشاء الأيقونة
echo ✓ ملفات التشغيل المباشر
echo ✓ دليل الاستخدام
echo.
echo هل تريد فتح مجلد النسخة الآن؟ (Y/N)
choice /c YN /n
if %errorlevel% equ 1 (
    echo فتح المجلد...
    start "" "%DESKTOP_COPY%"
)

echo.
echo اضغط أي مفتاح للخروج...
pause >nul
