@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion
title شركة وقود المستقبل - مثبت نظام إدارة محل الغاز

color 0A
echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██              شركة وقود المستقبل                          ██
echo ██         نظام إدارة محل تركيب وتصليح الغاز                ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

REM Set installation directory
set "INSTALL_DIR=%USERPROFILE%\AppData\Local\شركة وقود المستقبل"
set "DESKTOP_SHORTCUT=%USERPROFILE%\Desktop\شركة وقود المستقبل.lnk"
set "STARTMENU_DIR=%APPDATA%\Microsoft\Windows\Start Menu\Programs\شركة وقود المستقبل"

echo [INFO] مجلد التثبيت: %INSTALL_DIR%
echo.
echo [1/8] إنشاء مجلدات التطبيق...

REM Create application directory structure
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"
mkdir "%INSTALL_DIR%\assets" 2>nul
mkdir "%INSTALL_DIR%\styles" 2>nul
mkdir "%INSTALL_DIR%\scripts" 2>nul
mkdir "%INSTALL_DIR%\src" 2>nul

echo [2/8] نسخ ملفات التطبيق...

REM Copy all application files
for %%f in (*.html *.js *.json *.css) do (
    if exist "%%f" copy "%%f" "%INSTALL_DIR%\" >nul 2>&1
)

REM Copy directories
for %%d in (assets styles scripts src) do (
    if exist "%%d" xcopy "%%d\*" "%INSTALL_DIR%\%%d\" /Y /E /I >nul 2>&1
)

echo [3/8] إنشاء ملفات التشغيل...

REM Create main application launcher
(
echo @echo off
echo chcp 65001 ^>nul
echo title شركة وقود المستقبل - نظام إدارة محل الغاز
echo cd /d "%INSTALL_DIR%"
echo.
echo REM Check for Node.js and run accordingly
echo node --version ^>nul 2^>^&1
echo if %%errorlevel%% equ 0 ^(
echo     if exist "main.js" ^(
echo         echo تشغيل النظام...
echo         start /min cmd /c "node main.js"
echo     ^) else ^(
echo         start "" "index.html"
echo     ^)
echo ^) else ^(
echo     start "" "index.html"
echo ^)
echo exit
) > "%INSTALL_DIR%\تشغيل التطبيق.bat"

REM Create VBS launcher for silent execution
(
echo Set WshShell = CreateObject^("WScript.Shell"^)
echo Set fso = CreateObject^("Scripting.FileSystemObject"^)
echo scriptDir = "%INSTALL_DIR%"
echo batchFile = scriptDir ^& "\تشغيل التطبيق.bat"
echo If fso.FileExists^(batchFile^) Then
echo     WshShell.Run """" ^& batchFile ^& """", 0, False
echo Else
echo     htmlFile = scriptDir ^& "\index.html"
echo     If fso.FileExists^(htmlFile^) Then
echo         WshShell.Run """" ^& htmlFile ^& """", 1, False
echo     Else
echo         MsgBox "لم يتم العثور على ملفات التطبيق", 16, "خطأ"
echo     End If
echo End If
) > "%INSTALL_DIR%\تشغيل صامت.vbs"

echo [4/8] إنشاء أيقونة التطبيق...

REM Create a simple icon using PowerShell (fallback if no custom icon)
powershell -Command "& {Add-Type -AssemblyName System.Drawing; $bmp = New-Object System.Drawing.Bitmap(32,32); $g = [System.Drawing.Graphics]::FromImage($bmp); $g.FillRectangle([System.Drawing.Brushes]::Green, 0, 0, 32, 32); $g.FillRectangle([System.Drawing.Brushes]::White, 8, 8, 16, 16); $g.Dispose(); $bmp.Save('%INSTALL_DIR%\icon.png', [System.Drawing.Imaging.ImageFormat]::Png); $bmp.Dispose()}" 2>nul

echo [5/8] إنشاء اختصار سطح المكتب...

REM Create desktop shortcut
powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP_SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\تشغيل صامت.vbs'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.IconLocation = '%INSTALL_DIR%\icon.png'; $Shortcut.Description = 'شركة وقود المستقبل - نظام إدارة محل الغاز'; $Shortcut.Save()}" 2>nul

echo [6/8] إنشاء اختصار قائمة ابدأ...

REM Create Start Menu shortcut
if not exist "%STARTMENU_DIR%" mkdir "%STARTMENU_DIR%"
powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%STARTMENU_DIR%\شركة وقود المستقبل.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\تشغيل صامت.vbs'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.IconLocation = '%INSTALL_DIR%\icon.png'; $Shortcut.Description = 'شركة وقود المستقبل - نظام إدارة محل الغاز'; $Shortcut.Save()}" 2>nul

echo [7/8] إنشاء ملف إلغاء التثبيت...

REM Create uninstaller
(
echo @echo off
echo chcp 65001 ^>nul
echo title إلغاء تثبيت شركة وقود المستقبل
echo color 0C
echo.
echo هل أنت متأكد من إلغاء تثبيت نظام إدارة محل الغاز؟
echo.
echo اضغط Y للمتابعة أو N للإلغاء
echo.
echo choice /c YN /n /m "اختيارك: "
echo if %%errorlevel%% equ 2 goto :cancel
echo.
echo جاري إلغاء التثبيت...
echo.
echo حذف ملفات التطبيق...
echo rmdir /s /q "%INSTALL_DIR%" ^>nul 2^>^&1
echo.
echo حذف الاختصارات...
echo del "%DESKTOP_SHORTCUT%" ^>nul 2^>^&1
echo rmdir /s /q "%STARTMENU_DIR%" ^>nul 2^>^&1
echo.
echo تم إلغاء التثبيت بنجاح!
echo goto :end
echo.
echo :cancel
echo تم إلغاء العملية.
echo.
echo :end
echo pause
) > "%INSTALL_DIR%\إلغاء التثبيت.bat"

echo [8/8] إنهاء التثبيت...

echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██                   تم التثبيت بنجاح!                      ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.
echo ✓ تم تثبيت التطبيق في: %INSTALL_DIR%
echo ✓ تم إنشاء اختصار سطح المكتب: شركة وقود المستقبل
echo ✓ تم إنشاء اختصار قائمة ابدأ: شركة وقود المستقبل
echo.
echo طرق تشغيل التطبيق:
echo 1. انقر على اختصار سطح المكتب
echo 2. ابحث في قائمة ابدأ عن "شركة وقود المستقبل"
echo 3. شغل الملف: %INSTALL_DIR%\تشغيل صامت.vbs
echo.
echo لإلغاء التثبيت: %INSTALL_DIR%\إلغاء التثبيت.bat
echo.
echo اضغط أي مفتاح لإنهاء المثبت...
pause >nul

REM Optionally launch the application
echo.
echo هل تريد تشغيل التطبيق الآن؟ (Y/N)
choice /c YN /n /m "اختيارك: "
if %errorlevel% equ 1 (
    echo تشغيل التطبيق...
    start "" "%INSTALL_DIR%\تشغيل صامت.vbs"
)

exit
