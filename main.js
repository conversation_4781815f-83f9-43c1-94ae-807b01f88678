const { app, BrowserWindow } = require('electron');
const path = require('path');

function createWindow () {
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    icon: path.join(__dirname, 'assets/icons/company-logo.svg'),
    title: 'مؤسسة وقود المستقبل - نظام الإدارة',
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    },
    show: false // لا تظهر النافذة حتى تكون جاهزة
  });

  mainWindow.loadFile(path.join(__dirname, 'index.html'));

  // إظهار النافذة عندما تكون جاهزة
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    mainWindow.focus();
  });

  // إضافة قائمة التطبيق
  mainWindow.setMenuBarVisibility(false); // إخفاء شريط القوائم
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});
