@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion
title شركة وقود المستقبل - تشغيل متقدم

color 0A
echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██              شركة وقود المستقبل                          ██
echo ██         نظام إدارة محل تركيب وتصليح الغاز                ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

cd /d "%~dp0"

echo [INFO] مجلد التطبيق: %CD%
echo.

echo اختر طريقة التشغيل:
echo.
echo [1] تشغيل التطبيق في المتصفح (موصى به)
echo [2] تشغيل مع Node.js (إذا كان مثبت)
echo [3] تثبيت التطبيق في النظام
echo [4] إعداد أيقونة مخصصة
echo [5] فتح دليل الاستخدام
echo [6] خروج
echo.

choice /c 123456 /n /m "اختر رقم (1-6): "

if %errorlevel%==1 goto :web_mode
if %errorlevel%==2 goto :node_mode
if %errorlevel%==3 goto :install_mode
if %errorlevel%==4 goto :icon_mode
if %errorlevel%==5 goto :help_mode
if %errorlevel%==6 goto :exit

:web_mode
echo.
echo [WEB] تشغيل التطبيق في المتصفح...
echo.
if exist "index.html" (
    echo ✓ تم العثور على ملف التطبيق
    echo ✓ فتح التطبيق في المتصفح...
    start "" "index.html"
    echo.
    echo ✅ تم تشغيل التطبيق بنجاح!
    echo 🌐 تحقق من نافذة المتصفح
) else (
    echo ❌ خطأ: لم يتم العثور على ملف index.html
)
goto :end

:node_mode
echo.
echo [NODE] تشغيل مع Node.js...
echo.
node --version >nul 2>&1
if %errorlevel%==0 (
    echo ✓ تم العثور على Node.js
    if exist "main.js" (
        echo ✓ تشغيل الخادم المحلي...
        echo.
        echo 🚀 التطبيق يعمل على: http://localhost:3000
        echo 📝 اضغط Ctrl+C لإيقاف الخادم
        echo.
        node main.js
    ) else (
        echo ❌ لم يتم العثور على main.js
        echo 🔄 التبديل إلى الوضع الويب...
        goto :web_mode
    )
) else (
    echo ❌ Node.js غير مثبت
    echo 🔄 التبديل إلى الوضع الويب...
    goto :web_mode
)
goto :end

:install_mode
echo.
echo [INSTALL] تثبيت التطبيق في النظام...
echo.
if exist "installers\simple_installer.bat" (
    echo ✓ تشغيل المثبت...
    start "" "installers\simple_installer.bat"
    echo ✅ تم تشغيل المثبت
) else (
    echo ❌ لم يتم العثور على المثبت
)
goto :end

:icon_mode
echo.
echo [ICON] إعداد الأيقونة المخصصة...
echo.
if exist "tools\create_simple_icon.html" (
    echo ✓ فتح أداة إنشاء الأيقونة...
    start "" "tools\create_simple_icon.html"
)
if exist "tools\logo_to_icon_converter.html" (
    echo ✓ فتح أداة تحويل الشعار...
    start "" "tools\logo_to_icon_converter.html"
)
echo ✅ تم فتح أدوات الأيقونة
goto :end

:help_mode
echo.
echo [HELP] دليل الاستخدام...
echo.
if exist "اقرأني - README.txt" (
    echo ✓ فتح دليل الاستخدام...
    start "" "اقرأني - README.txt"
    echo ✅ تم فتح الدليل
) else (
    echo ❌ لم يتم العثور على دليل الاستخدام
)
goto :end

:exit
echo.
echo 👋 شكراً لاستخدام نظام شركة وقود المستقبل
goto :end

:end
echo.
echo ════════════════════════════════════════════════════════════════
echo   للحصول على المساعدة، راجع ملف "اقرأني - README.txt"
echo ════════════════════════════════════════════════════════════════
echo.
echo اضغط أي مفتاح للخروج...
pause >nul
