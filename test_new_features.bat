@echo off
setlocal enabledelayedexpansion
title اختبار الميزات الجديدة - شركة وقود المستقبل

echo ========================================
echo   اختبار الميزات الجديدة
echo   شركة وقود المستقبل
echo ========================================
echo.

echo تم إضافة الميزات التالية:
echo.
echo ✓ إدارة الموردين
echo   - إضافة وتعديل وحذف الموردين
echo   - تتبع المشتريات من كل مورد
echo   - إحصائيات الموردين
echo.
echo ✓ إدارة المخزون
echo   - إضافة وتعديل الأصناف
echo   - تتبع الكميات والحد الأدنى
echo   - تعديل المخزون مع سجل التغييرات
echo   - تصنيف الأصناف (قطع غيار، أدوات، إكسسوارات، مواد استهلاكية)
echo.
echo ✓ إدارة المبيعات
echo   - إنشاء فواتير البيع
echo   - ربط المبيعات بالزبائن
echo   - خصم تلقائي من المخزون
echo   - حساب الضرائب والإجماليات
echo   - إحصائيات المبيعات اليومية والشهرية
echo.
echo ✓ إدارة المشتريات
echo   - إنشاء فواتير الشراء
echo   - ربط المشتريات بالموردين
echo   - إضافة تلقائية للمخزون
echo   - تحديث أسعار الشراء
echo   - إحصائيات المشتريات
echo.
echo ✓ لوحة التحكم المحدثة
echo   - عدادات جديدة للمخزون والمبيعات والمشتريات
echo   - تنبيهات المخزون المنخفض
echo   - إحصائيات الموردين النشطين
echo.
echo ✓ تنسيق العملة
echo   - عرض الأسعار بالدينار الجزائري (د.ج)
echo   - تنسيق الأرقام باللغة العربية
echo.
echo ========================================
echo.

echo هل تريد تشغيل التطبيق لاختبار الميزات الجديدة؟ (Y/N)
choice /c YN /n
if %errorlevel% equ 1 (
    echo.
    echo تشغيل التطبيق...
    start "" "index.html"
    echo.
    echo ✅ تم تشغيل التطبيق!
    echo.
    echo للاختبار:
    echo 1. انتقل إلى قسم "الموردين" لإضافة موردين جدد
    echo 2. انتقل إلى قسم "المخزون" لإضافة أصناف
    echo 3. انتقل إلى قسم "المشتريات" لإنشاء فاتورة شراء
    echo 4. انتقل إلى قسم "المبيعات" لإنشاء فاتورة بيع
    echo 5. تحقق من لوحة التحكم لرؤية الإحصائيات المحدثة
    echo.
) else (
    echo.
    echo تم إلغاء التشغيل.
)

echo.
echo ملاحظات مهمة:
echo.
echo 🔹 جميع البيانات محفوظة محلياً في المتصفح
echo 🔹 يمكن طباعة التقارير من كل قسم
echo 🔹 النظام يدعم البحث والتصفية في جميع الأقسام
echo 🔹 تم إضافة التحقق من صحة البيانات
echo 🔹 النظام يتتبع تلقائياً تغييرات المخزون
echo.
echo اضغط أي مفتاح للخروج...
pause >nul
