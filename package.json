{"name": "gas-car-shop", "productName": "نظام إدارة محل تركيب وتصليح الغاز للسيارات", "version": "1.0.0", "description": "نظام متكامل لإدارة محل تركيب وتصليح الغاز للسيارات", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder build", "pack": "electron-builder --dir", "dist": "electron-builder --win portable", "postinstall": "electron-builder install-app-deps", "electron:start": "electron .", "electron:build": "electron-builder"}, "keywords": ["gas", "shop", "management", "electron", "desktop"], "author": {"name": "Your Name", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.0.0"}, "dependencies": {}, "build": {"appId": "com.gascarshop.app", "productName": "Gas Car Shop", "directories": {"buildResources": "assets"}, "files": ["main.js", "frontend/build/**/*"], "win": {"target": ["nsis"], "icon": "build/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "نظام إدارة محل الغاز", "artifactName": "GasShopManagement-Setup-${version}.${ext}", "uninstallDisplayName": "نظام إدارة مؤسسة وقود المستقبل", "installerIcon": "assets/icon.ico", "uninstallerIcon": "assets/icon.ico", "installerHeaderIcon": "assets/icon.ico", "displayLanguageSelector": true, "multiLanguageInstaller": true, "language": "1025", "license": "LICENSE"}}}