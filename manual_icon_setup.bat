@echo off
setlocal enabledelayedexpansion
title Manual Icon Setup - Future Fuel Corporation

echo ========================================
echo   Manual Icon Setup Guide
echo   Future Fuel Corporation
echo ========================================
echo.

set "INSTALL_DIR=%USERPROFILE%\AppData\Local\GasShopManagement"
set "DESKTOP_SHORTCUT=%USERPROFILE%\Desktop\Gas Shop Management.lnk"

echo Current installation directory: %INSTALL_DIR%
echo.

echo Step 1: Opening icon creation tool...
start "" "create_simple_icon.html"

echo.
echo Step 2: Manual Instructions
echo ========================================
echo.
echo 1. In the browser window that just opened:
echo    - Click "Create Icon" button
echo    - Click "Download PNG" button
echo    - Click "Download ICO" button
echo.
echo 2. Save the downloaded files to your Downloads folder
echo.
echo 3. Copy the icon files to the application folder:
echo    - Copy future-fuel-icon.png to: %INSTALL_DIR%\assets\
echo    - Rename it to: icon.png
echo.
echo 4. Update the desktop shortcut:
echo    - Right-click on "Gas Shop Management" shortcut on desktop
echo    - Select "Properties"
echo    - Click "Change Icon"
echo    - Click "Browse"
echo    - Navigate to: %INSTALL_DIR%\assets\
echo    - Select icon.png
echo    - Click OK twice
echo.
echo ========================================
echo.

echo Would you like me to open the application folder for you? (Y/N)
choice /c YN /n
if %errorlevel% equ 1 (
    echo Opening application folder...
    start "" "%INSTALL_DIR%\assets"
    echo.
    echo The assets folder is now open.
    echo Copy your downloaded icon files here.
)

echo.
echo Would you like to test the application? (Y/N)
choice /c YN /n
if %errorlevel% equ 1 (
    echo Starting application...
    start "" "%INSTALL_DIR%\launch_silent.vbs"
)

echo.
echo ========================================
echo   Setup Complete!
echo ========================================
echo.
echo Your application is ready to use:
echo - Desktop shortcut: Gas Shop Management
echo - Start Menu: Search for "Gas Shop" or "Future Fuel"
echo.
echo To add custom icon:
echo 1. Download icon from the browser tool
echo 2. Copy to: %INSTALL_DIR%\assets\icon.png
echo 3. Right-click desktop shortcut > Properties > Change Icon
echo.
echo Press any key to exit...
pause >nul
