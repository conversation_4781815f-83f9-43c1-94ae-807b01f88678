# نظام إدارة مؤسسة وقود المستقبل

نظام متكامل لإدارة مؤسسة وقود المستقبل، يتضمن إدارة الزبائن، السيارات، بطاقات الغاز، المواعيد، الديون، وإصدار التقارير.

## المميزات

- إدارة الزبائن والسيارات
- إدارة بطاقات الغاز ومتابعة تواريخ انتهائها
- جدولة المواعيد وإرسال تذكيرات
- إدارة الديون ومتابعة الدفعات
- إصدار التقارير وطباعتها
- تصدير التقارير بصيغة PDF
- واجهة مستخدم سهلة الاستخدام
- دعم الوضع المظلم (Dark Mode)
- حفظ البيانات محلياً

## متطلبات التشغيل

- نظام تشغيل Windows 10 أو أحدث
- مساحة تخزين 100 ميجابايت على الأقل

## تثبيت البرنامج

### الطريقة الأولى: تثبيت البرنامج من ملف التثبيت

1. قم بتحميل ملف التثبيت من [هنا](https://example.com/download)
2. قم بتشغيل ملف التثبيت واتبع التعليمات
3. بعد اكتمال التثبيت، يمكنك تشغيل البرنامج من سطح المكتب أو قائمة ابدأ

### الطريقة الثانية: تشغيل البرنامج من المصدر

إذا كنت مطوراً أو ترغب في تشغيل البرنامج من المصدر، اتبع الخطوات التالية:

1. تأكد من تثبيت [Node.js](https://nodejs.org/) (الإصدار 14 أو أحدث)
2. قم بتنزيل أو استنساخ المشروع
3. افتح موجه الأوامر (Command Prompt) في مجلد المشروع
4. قم بتثبيت الاعتمادات:
   ```
   npm install
   ```
5. قم بتشغيل البرنامج:
   ```
   npm start
   ```

### الطريقة الثالثة: بناء ملف التثبيت بنفسك

1. اتبع الخطوات من 1 إلى 4 في الطريقة الثانية
2. قم ببناء ملف التثبيت:
   ```
   npm run dist
   ```
3. ستجد ملف التثبيت في مجلد `dist`

## استخدام البرنامج

1. قم بتشغيل البرنامج من سطح المكتب أو قائمة ابدأ
2. استخدم القائمة الجانبية للتنقل بين الأقسام المختلفة
3. يمكنك إضافة زبائن وسيارات وبطاقات غاز ومواعيد وديون
4. يمكنك طباعة التقارير أو تصديرها بصيغة PDF

## الدعم الفني

للحصول على الدعم الفني، يرجى التواصل عبر:
- البريد الإلكتروني: <EMAIL>
- الهاتف: **********

## الترخيص

هذا البرنامج مرخص بموجب [رخصة MIT](LICENSE).
