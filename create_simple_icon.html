<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء أيقونة شركة وقود المستقبل</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            max-width: 600px;
        }
        h1 {
            color: #2E7D32;
            margin-bottom: 30px;
        }
        canvas {
            border: 3px solid #4CAF50;
            border-radius: 15px;
            margin: 20px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        button {
            background: linear-gradient(45deg, #4CAF50, #2E7D32);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }
        .instructions {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: right;
        }
        .download-links {
            margin: 20px 0;
        }
        .download-links a {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚗 إنشاء أيقونة شركة وقود المستقبل</h1>
        
        <canvas id="iconCanvas" width="256" height="256"></canvas>
        
        <div>
            <button onclick="createIcon()">🎨 إنشاء الأيقونة</button>
            <button onclick="downloadPNG()">📥 تنزيل PNG</button>
            <button onclick="downloadICO()">🖼️ تنزيل ICO</button>
            <button onclick="installIcon()">⚡ تثبيت في التطبيق</button>
        </div>
        
        <div class="download-links">
            <a id="downloadPNG" download="future-fuel-icon.png"></a>
            <a id="downloadICO" download="future-fuel-icon.ico"></a>
        </div>
        
        <div class="instructions">
            <h3>📋 تعليمات التثبيت:</h3>
            <ol>
                <li>انقر على "إنشاء الأيقونة" أعلاه</li>
                <li>انقر على "تثبيت في التطبيق"</li>
                <li>احفظ الملفات في مجلد التنزيلات</li>
                <li>شغل ملف "update_icon.bat" الذي سيتم إنشاؤه</li>
            </ol>
        </div>
    </div>

    <script>
        function createIcon() {
            const canvas = document.getElementById('iconCanvas');
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, 256, 256);
            
            // Background gradient (green theme)
            const gradient = ctx.createRadialGradient(128, 128, 0, 128, 128, 180);
            gradient.addColorStop(0, '#4CAF50');
            gradient.addColorStop(0.7, '#2E7D32');
            gradient.addColorStop(1, '#1B5E20');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 256, 256);
            
            // Border
            ctx.strokeStyle = '#0D4E12';
            ctx.lineWidth = 6;
            ctx.strokeRect(3, 3, 250, 250);
            
            // Car body (main shape)
            ctx.fillStyle = '#FFFFFF';
            ctx.shadowColor = 'rgba(0,0,0,0.3)';
            ctx.shadowBlur = 8;
            ctx.shadowOffsetX = 2;
            ctx.shadowOffsetY = 2;
            
            // Car main body
            ctx.beginPath();
            ctx.ellipse(128, 150, 90, 35, 0, 0, 2 * Math.PI);
            ctx.fill();
            
            // Car roof
            ctx.beginPath();
            ctx.ellipse(128, 125, 60, 25, 0, 0, 2 * Math.PI);
            ctx.fill();
            
            // Car windows
            ctx.fillStyle = '#87CEEB';
            ctx.beginPath();
            ctx.ellipse(128, 125, 45, 18, 0, 0, 2 * Math.PI);
            ctx.fill();
            
            // Wheels
            ctx.fillStyle = '#333333';
            ctx.shadowBlur = 4;
            ctx.beginPath();
            ctx.arc(85, 170, 18, 0, 2 * Math.PI);
            ctx.fill();
            ctx.beginPath();
            ctx.arc(171, 170, 18, 0, 2 * Math.PI);
            ctx.fill();
            
            // Wheel rims
            ctx.fillStyle = '#666666';
            ctx.beginPath();
            ctx.arc(85, 170, 10, 0, 2 * Math.PI);
            ctx.fill();
            ctx.beginPath();
            ctx.arc(171, 170, 10, 0, 2 * Math.PI);
            ctx.fill();
            
            // Gas pump nozzle
            ctx.fillStyle = '#FFFFFF';
            ctx.shadowBlur = 6;
            ctx.fillRect(185, 110, 45, 18);
            ctx.fillRect(230, 105, 20, 28);
            
            // Gas pump handle
            ctx.fillStyle = '#FF5722';
            ctx.fillRect(235, 115, 10, 8);
            
            // Reset shadow
            ctx.shadowColor = 'transparent';
            ctx.shadowBlur = 0;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 0;
            
            // Arabic company name
            ctx.fillStyle = '#FFFFFF';
            ctx.font = 'bold 28px Arial';
            ctx.textAlign = 'center';
            ctx.strokeStyle = '#1B5E20';
            ctx.lineWidth = 3;
            
            // Main text "وقود"
            ctx.strokeText('وقود', 128, 60);
            ctx.fillText('وقود', 128, 60);
            
            // Subtitle "المستقبل"
            ctx.font = 'bold 18px Arial';
            ctx.lineWidth = 2;
            ctx.strokeText('المستقبل', 128, 35);
            ctx.fillText('المستقبل', 128, 35);
            
            // English text "GAS"
            ctx.fillStyle = '#81C784';
            ctx.font = 'bold 24px Arial';
            ctx.strokeStyle = '#2E7D32';
            ctx.strokeText('GAS', 128, 210);
            ctx.fillText('GAS', 128, 210);
            
            // Small "FUTURE" text
            ctx.font = 'bold 14px Arial';
            ctx.fillStyle = '#A5D6A7';
            ctx.fillText('FUTURE', 128, 230);
        }
        
        function downloadPNG() {
            const canvas = document.getElementById('iconCanvas');
            const link = document.getElementById('downloadPNG');
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        function downloadICO() {
            const canvas = document.getElementById('iconCanvas');
            // Create smaller version for ICO
            const smallCanvas = document.createElement('canvas');
            smallCanvas.width = 32;
            smallCanvas.height = 32;
            const ctx = smallCanvas.getContext('2d');
            ctx.drawImage(canvas, 0, 0, 32, 32);
            
            const link = document.getElementById('downloadICO');
            link.href = smallCanvas.toDataURL('image/png');
            link.click();
        }
        
        function installIcon() {
            // Download both formats
            downloadPNG();
            downloadICO();
            
            // Create installation script
            const scriptContent = `@echo off
setlocal enabledelayedexpansion
title تحديث أيقونة شركة وقود المستقبل

echo ========================================
echo   تحديث أيقونة شركة وقود المستقبل
echo ========================================
echo.

set "INSTALL_DIR=%USERPROFILE%\\AppData\\Local\\GasShopManagement"
set "DESKTOP_SHORTCUT=%USERPROFILE%\\Desktop\\Gas Shop Management.lnk"

echo نسخ ملفات الأيقونة...
if exist "future-fuel-icon.png" copy "future-fuel-icon.png" "%INSTALL_DIR%\\assets\\icon.png" >nul 2>&1
if exist "future-fuel-icon.ico" copy "future-fuel-icon.ico" "%INSTALL_DIR%\\assets\\icon.ico" >nul 2>&1

echo تحديث اختصار سطح المكتب...
del "%DESKTOP_SHORTCUT%" >nul 2>&1
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP_SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\\launch_silent.vbs'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.IconLocation = '%INSTALL_DIR%\\assets\\icon.ico'; $Shortcut.Description = 'شركة وقود المستقبل'; $Shortcut.Save()" 2>nul

echo.
echo تم تحديث الأيقونة بنجاح!
echo تحقق من اختصار سطح المكتب الآن.
echo.
pause`;
            
            const blob = new Blob([scriptContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'update_icon.bat';
            a.click();
            URL.revokeObjectURL(url);
            
            alert('تم تنزيل الأيقونات وملف التحديث!\\n\\nالآن:\\n1. شغل ملف "update_icon.bat"\\n2. تحقق من اختصار سطح المكتب');
        }
        
        // Create icon on page load
        window.onload = createIcon;
    </script>
</body>
</html>
