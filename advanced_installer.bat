@echo off
setlocal enabledelayedexpansion
title Gas Shop Management System - Installer

echo ========================================
echo   Gas Shop Management System Installer
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running with administrator privileges...
) else (
    echo This installer requires administrator privileges.
    echo Please right-click and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo.
echo Checking system requirements...

REM Check Windows version
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo Windows version: %VERSION%

REM Create application directory
set "INSTALL_DIR=C:\Program Files\GasShopManagement"
echo.
echo Creating installation directory: %INSTALL_DIR%

if not exist "%INSTALL_DIR%" (
    mkdir "%INSTALL_DIR%"
    if !errorlevel! neq 0 (
        echo Error: Failed to create installation directory
        pause
        exit /b 1
    )
)

REM Create subdirectories
mkdir "%INSTALL_DIR%\assets" 2>nul
mkdir "%INSTALL_DIR%\styles" 2>nul
mkdir "%INSTALL_DIR%\scripts" 2>nul

echo.
echo Copying application files...

REM Copy main files
copy "index.html" "%INSTALL_DIR%\" >nul 2>&1
copy "main.js" "%INSTALL_DIR%\" >nul 2>&1
copy "config.json" "%INSTALL_DIR%\" >nul 2>&1
copy "package.json" "%INSTALL_DIR%\" >nul 2>&1

REM Copy assets
xcopy "assets\*" "%INSTALL_DIR%\assets\" /Y /E >nul 2>&1
xcopy "styles\*" "%INSTALL_DIR%\styles\" /Y /E >nul 2>&1
xcopy "scripts\*" "%INSTALL_DIR%\scripts\" /Y /E >nul 2>&1

REM Copy Node.js executable and modules (portable version)
if exist "node_modules" (
    echo Copying Node.js modules...
    xcopy "node_modules" "%INSTALL_DIR%\node_modules\" /Y /E /I >nul 2>&1
)

echo.
echo Creating startup script...

REM Create a startup script
(
echo @echo off
echo title Gas Shop Management System
echo cd /d "%INSTALL_DIR%"
echo.
echo REM Check if Node.js is available
echo node --version ^>nul 2^>^&1
echo if %%errorlevel%% neq 0 ^(
echo     echo Node.js is not installed. Opening web version...
echo     start "" "index.html"
echo ^) else ^(
echo     echo Starting Gas Shop Management System...
echo     npm start
echo ^)
) > "%INSTALL_DIR%\start_app.bat"

echo.
echo Creating desktop shortcut...

REM Create desktop shortcut using PowerShell
powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%PUBLIC%\Desktop\Gas Shop Management.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\start_app.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.IconLocation = '%INSTALL_DIR%\assets\icon.ico'; $Shortcut.Description = 'Gas Shop Management System'; $Shortcut.Save()}"

echo.
echo Creating Start Menu shortcut...

REM Create Start Menu shortcut
if not exist "%PROGRAMDATA%\Microsoft\Windows\Start Menu\Programs\Gas Shop Management" (
    mkdir "%PROGRAMDATA%\Microsoft\Windows\Start Menu\Programs\Gas Shop Management"
)

powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%PROGRAMDATA%\Microsoft\Windows\Start Menu\Programs\Gas Shop Management\Gas Shop Management.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\start_app.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.IconLocation = '%INSTALL_DIR%\assets\icon.ico'; $Shortcut.Description = 'Gas Shop Management System'; $Shortcut.Save()}"

echo.
echo Creating uninstaller...

REM Create uninstaller
(
echo @echo off
echo title Gas Shop Management System - Uninstaller
echo.
echo Are you sure you want to uninstall Gas Shop Management System?
echo Press Y to continue or any other key to cancel...
echo.
echo choice /c YN /n
echo if %%errorlevel%% equ 2 goto :cancel
echo.
echo Removing application files...
echo rmdir /s /q "%INSTALL_DIR%" ^>nul 2^>^&1
echo.
echo Removing shortcuts...
echo del "%PUBLIC%\Desktop\Gas Shop Management.lnk" ^>nul 2^>^&1
echo rmdir /s /q "%PROGRAMDATA%\Microsoft\Windows\Start Menu\Programs\Gas Shop Management" ^>nul 2^>^&1
echo.
echo Uninstallation completed successfully!
echo goto :end
echo.
echo :cancel
echo Uninstallation cancelled.
echo.
echo :end
echo pause
) > "%INSTALL_DIR%\uninstall.bat"

echo.
echo ========================================
echo   Installation completed successfully!
echo ========================================
echo.
echo The application has been installed to: %INSTALL_DIR%
echo.
echo You can start the application from:
echo - Desktop shortcut: Gas Shop Management
echo - Start Menu: Gas Shop Management
echo - Or run: %INSTALL_DIR%\start_app.bat
echo.
echo To uninstall, run: %INSTALL_DIR%\uninstall.bat
echo.
echo Press any key to exit...
pause >nul
