// Preload script
const { contextBridge, ipcRenderer } = require('electron');
const fs = require('fs');
const path = require('path');

// تحديد مسار حفظ البيانات
const appDataPath = path.join(process.env.APPDATA || process.env.HOME, 'GasShopManagement');

// التأكد من وجود مجلد البيانات
if (!fs.existsSync(appDataPath)) {
  fs.mkdirSync(appDataPath, { recursive: true });
}

// تحديد مسار ملف البيانات
const dataFilePath = path.join(appDataPath, 'data.json');

// تعريف واجهة API للتفاعل مع Electron
contextBridge.exposeInMainWorld('electronAPI', {
  // حفظ البيانات
  saveData: (data) => {
    try {
      fs.writeFileSync(dataFilePath, JSON.stringify(data, null, 2), 'utf-8');
      return true;
    } catch (error) {
      console.error('Error saving data:', error);
      return false;
    }
  },
  
  // تحميل البيانات
  loadData: () => {
    try {
      if (fs.existsSync(dataFilePath)) {
        const data = fs.readFileSync(dataFilePath, 'utf-8');
        return JSON.parse(data);
      }
      return null;
    } catch (error) {
      console.error('Error loading data:', error);
      return null;
    }
  },
  
  // إنشاء نسخة احتياطية
  createBackup: () => {
    try {
      const backupDir = path.join(appDataPath, 'backups');
      if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir, { recursive: true });
      }
      
      const date = new Date();
      const backupFileName = `backup_${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}_${date.getHours()}-${date.getMinutes()}.json`;
      const backupFilePath = path.join(backupDir, backupFileName);
      
      if (fs.existsSync(dataFilePath)) {
        fs.copyFileSync(dataFilePath, backupFilePath);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error creating backup:', error);
      return false;
    }
  },
  
  // استعادة نسخة احتياطية
  restoreBackup: (backupFileName) => {
    try {
      const backupDir = path.join(appDataPath, 'backups');
      const backupFilePath = path.join(backupDir, backupFileName);
      
      if (fs.existsSync(backupFilePath)) {
        fs.copyFileSync(backupFilePath, dataFilePath);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error restoring backup:', error);
      return false;
    }
  },
  
  // الحصول على قائمة النسخ الاحتياطية
  getBackupsList: () => {
    try {
      const backupDir = path.join(appDataPath, 'backups');
      if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir, { recursive: true });
        return [];
      }
      
      return fs.readdirSync(backupDir)
        .filter(file => file.endsWith('.json'))
        .map(file => {
          const filePath = path.join(backupDir, file);
          const stats = fs.statSync(filePath);
          return {
            name: file,
            date: stats.mtime,
            size: stats.size
          };
        })
        .sort((a, b) => b.date - a.date);
    } catch (error) {
      console.error('Error getting backups list:', error);
      return [];
    }
  },
  
  // تصدير البيانات
  exportData: (filePath) => {
    try {
      if (fs.existsSync(dataFilePath)) {
        fs.copyFileSync(dataFilePath, filePath);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error exporting data:', error);
      return false;
    }
  },
  
  // استيراد البيانات
  importData: (filePath) => {
    try {
      if (fs.existsSync(filePath)) {
        fs.copyFileSync(filePath, dataFilePath);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error importing data:', error);
      return false;
    }
  },
  
  // إرسال رسائل إلى العملية الرئيسية
  send: (channel, data) => {
    ipcRenderer.send(channel, data);
  },
  
  // استقبال رسائل من العملية الرئيسية
  receive: (channel, func) => {
    ipcRenderer.on(channel, (event, ...args) => func(...args));
  }
});
