@echo off
title Gas Shop Management System - User Installer

echo ========================================
echo   Gas Shop Management System Installer
echo   (User Installation - No Admin Required)
echo ========================================
echo.

REM Set installation directory in user folder
set "INSTALL_DIR=%USERPROFILE%\AppData\Local\GasShopManagement"

echo Installing to: %INSTALL_DIR%
echo.

REM Create application directory
if not exist "%INSTALL_DIR%" (
    mkdir "%INSTALL_DIR%"
)

REM Create subdirectories
mkdir "%INSTALL_DIR%\assets" 2>nul
mkdir "%INSTALL_DIR%\styles" 2>nul
mkdir "%INSTALL_DIR%\scripts" 2>nul

echo Copying application files...

REM Copy main files
copy "index.html" "%INSTALL_DIR%\" >nul 2>&1
copy "main.js" "%INSTALL_DIR%\" >nul 2>&1
copy "config.json" "%INSTALL_DIR%\" >nul 2>&1
copy "package.json" "%INSTALL_DIR%\" >nul 2>&1

REM Copy assets
xcopy "assets\*" "%INSTALL_DIR%\assets\" /Y /E >nul 2>&1
xcopy "styles\*" "%INSTALL_DIR%\styles\" /Y /E >nul 2>&1
xcopy "scripts\*" "%INSTALL_DIR%\scripts\" /Y /E >nul 2>&1

echo.
echo Creating startup script...

REM Create a startup script that works without Node.js
(
echo @echo off
echo title Gas Shop Management System
echo cd /d "%INSTALL_DIR%"
echo.
echo echo Starting Gas Shop Management System...
echo echo.
echo.
echo REM Try to start with Node.js first
echo node --version ^>nul 2^>^&1
echo if %%errorlevel%% equ 0 ^(
echo     echo Using Node.js...
echo     if exist "main.js" ^(
echo         node main.js
echo     ^) else ^(
echo         echo Opening web version...
echo         start "" "index.html"
echo     ^)
echo ^) else ^(
echo     echo Node.js not found. Opening web version...
echo     start "" "index.html"
echo ^)
echo.
echo pause
) > "%INSTALL_DIR%\start_app.bat"

echo.
echo Creating desktop shortcut...

REM Create desktop shortcut
powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\Gas Shop Management.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\start_app.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.IconLocation = '%INSTALL_DIR%\assets\icon.ico'; $Shortcut.Description = 'Gas Shop Management System'; $Shortcut.Save()}"

echo.
echo Creating Start Menu shortcut...

REM Create Start Menu shortcut in user folder
if not exist "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Gas Shop Management" (
    mkdir "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Gas Shop Management"
)

powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%APPDATA%\Microsoft\Windows\Start Menu\Programs\Gas Shop Management\Gas Shop Management.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\start_app.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.IconLocation = '%INSTALL_DIR%\assets\icon.ico'; $Shortcut.Description = 'Gas Shop Management System'; $Shortcut.Save()}"

echo.
echo Creating web launcher...

REM Create a direct web launcher
(
echo @echo off
echo title Gas Shop Management System - Web Version
echo cd /d "%INSTALL_DIR%"
echo echo Opening Gas Shop Management System in your browser...
echo start "" "index.html"
) > "%INSTALL_DIR%\start_web.bat"

echo.
echo Creating uninstaller...

REM Create uninstaller
(
echo @echo off
echo title Gas Shop Management System - Uninstaller
echo.
echo Are you sure you want to uninstall Gas Shop Management System?
echo Press Y to continue or any other key to cancel...
echo.
echo choice /c YN /n
echo if %%errorlevel%% equ 2 goto :cancel
echo.
echo Removing application files...
echo rmdir /s /q "%INSTALL_DIR%" ^>nul 2^>^&1
echo.
echo Removing shortcuts...
echo del "%USERPROFILE%\Desktop\Gas Shop Management.lnk" ^>nul 2^>^&1
echo rmdir /s /q "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Gas Shop Management" ^>nul 2^>^&1
echo.
echo Uninstallation completed successfully!
echo goto :end
echo.
echo :cancel
echo Uninstallation cancelled.
echo.
echo :end
echo pause
) > "%INSTALL_DIR%\uninstall.bat"

echo.
echo ========================================
echo   Installation completed successfully!
echo ========================================
echo.
echo The application has been installed to: %INSTALL_DIR%
echo.
echo You can start the application from:
echo - Desktop shortcut: Gas Shop Management
echo - Start Menu: Gas Shop Management
echo.
echo Available launchers:
echo - Full version: %INSTALL_DIR%\start_app.bat
echo - Web version: %INSTALL_DIR%\start_web.bat
echo.
echo To uninstall, run: %INSTALL_DIR%\uninstall.bat
echo.
echo Press any key to exit...
pause >nul
