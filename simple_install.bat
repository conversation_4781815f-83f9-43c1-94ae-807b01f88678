@echo off
echo Installing Gas Shop Management System...
echo.

REM Copy the executable to desktop
echo Copying program to desktop...
copy "dist\win-unpacked\*.exe" "%USERPROFILE%\Desktop\" /Y

REM Copy assets folder
echo Copying assets...
if not exist "%USERPROFILE%\Desktop\assets" mkdir "%USERPROFILE%\Desktop\assets"
xcopy "assets\*" "%USERPROFILE%\Desktop\assets\" /Y /E

REM Copy config file
copy "config.json" "%USERPROFILE%\Desktop\" /Y

echo.
echo Installation completed!
echo You can find the program on your desktop.
echo.
pause
