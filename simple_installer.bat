@echo off
setlocal enabledelayedexpansion
title Gas Shop Management System - Installer

echo ========================================
echo   Gas Shop Management System Installer
echo   Future Fuel Corporation
echo ========================================
echo.

REM Set installation directory
set "INSTALL_DIR=%USERPROFILE%\AppData\Local\GasShopManagement"
set "DESKTOP_SHORTCUT=%USERPROFILE%\Desktop\Gas Shop Management.lnk"
set "STARTMENU_DIR=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Gas Shop Management"

echo Installing to: %INSTALL_DIR%
echo.

echo [1/6] Creating application directories...
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"
mkdir "%INSTALL_DIR%\assets" 2>nul
mkdir "%INSTALL_DIR%\styles" 2>nul
mkdir "%INSTALL_DIR%\scripts" 2>nul
mkdir "%INSTALL_DIR%\src" 2>nul

echo [2/6] Copying application files...
for %%f in (*.html *.js *.json *.css) do (
    if exist "%%f" copy "%%f" "%INSTALL_DIR%\" >nul 2>&1
)

for %%d in (assets styles scripts src) do (
    if exist "%%d" xcopy "%%d\*" "%INSTALL_DIR%\%%d\" /Y /E /I >nul 2>&1
)

echo [3/6] Creating application launcher...
(
echo @echo off
echo title Gas Shop Management System
echo cd /d "%INSTALL_DIR%"
echo.
echo node --version ^>nul 2^>^&1
echo if %%errorlevel%% equ 0 ^(
echo     if exist "main.js" ^(
echo         echo Starting Gas Shop Management System...
echo         node main.js
echo     ^) else ^(
echo         start "" "index.html"
echo     ^)
echo ^) else ^(
echo     start "" "index.html"
echo ^)
) > "%INSTALL_DIR%\start_app.bat"

echo [4/6] Creating silent launcher...
(
echo Set WshShell = CreateObject^("WScript.Shell"^)
echo Set fso = CreateObject^("Scripting.FileSystemObject"^)
echo scriptDir = "%INSTALL_DIR%"
echo batchFile = scriptDir ^& "\start_app.bat"
echo If fso.FileExists^(batchFile^) Then
echo     WshShell.Run """" ^& batchFile ^& """", 0, False
echo Else
echo     htmlFile = scriptDir ^& "\index.html"
echo     If fso.FileExists^(htmlFile^) Then
echo         WshShell.Run """" ^& htmlFile ^& """", 1, False
echo     End If
echo End If
) > "%INSTALL_DIR%\launch_silent.vbs"

echo [5/6] Creating shortcuts...
REM Create desktop shortcut
powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP_SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\launch_silent.vbs'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Gas Shop Management System'; $Shortcut.Save()}" 2>nul

REM Create Start Menu shortcut
if not exist "%STARTMENU_DIR%" mkdir "%STARTMENU_DIR%"
powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%STARTMENU_DIR%\Gas Shop Management.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\launch_silent.vbs'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Gas Shop Management System'; $Shortcut.Save()}" 2>nul

echo [6/6] Creating uninstaller...
(
echo @echo off
echo title Gas Shop Management System - Uninstaller
echo.
echo Are you sure you want to uninstall Gas Shop Management System?
echo Press Y to continue or N to cancel...
echo.
echo choice /c YN /n
echo if %%errorlevel%% equ 2 goto :cancel
echo.
echo Removing application files...
echo rmdir /s /q "%INSTALL_DIR%" ^>nul 2^>^&1
echo.
echo Removing shortcuts...
echo del "%DESKTOP_SHORTCUT%" ^>nul 2^>^&1
echo rmdir /s /q "%STARTMENU_DIR%" ^>nul 2^>^&1
echo.
echo Uninstallation completed successfully!
echo goto :end
echo.
echo :cancel
echo Uninstallation cancelled.
echo.
echo :end
echo pause
) > "%INSTALL_DIR%\uninstall.bat"

echo.
echo ========================================
echo   Installation completed successfully!
echo ========================================
echo.
echo Application installed to: %INSTALL_DIR%
echo.
echo You can start the application from:
echo - Desktop shortcut: Gas Shop Management
echo - Start Menu: Gas Shop Management
echo.
echo To uninstall: %INSTALL_DIR%\uninstall.bat
echo.
echo Would you like to start the application now? (Y/N)
choice /c YN /n
if %errorlevel% equ 1 (
    echo Starting application...
    start "" "%INSTALL_DIR%\launch_silent.vbs"
)

echo.
echo Press any key to exit installer...
pause >nul
