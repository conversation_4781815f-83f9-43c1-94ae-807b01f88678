@echo off
chcp 65001 >nul
echo ========================================
echo    تثبيت نظام إدارة مؤسسة وقود المستقبل
echo ========================================
echo.

echo جاري التحقق من متطلبات النظام...
echo.

REM التحقق من وجود مجلد البرنامج
if not exist "C:\Program Files\نظام إدارة محل الغاز" (
    echo إنشاء مجلد البرنامج...
    mkdir "C:\Program Files\نظام إدارة محل الغاز"
)

REM إنشاء مجلد assets
if not exist "C:\Program Files\نظام إدارة محل الغاز\assets" (
    mkdir "C:\Program Files\نظام إدارة محل الغاز\assets"
)

REM نسخ الملفات
echo نسخ ملفات البرنامج...
xcopy "dist\win-unpacked\*" "C:\Program Files\نظام إدارة محل الغاز\" /Y /E >nul
xcopy "assets\*" "C:\Program Files\نظام إدارة محل الغاز\assets\" /Y /E >nul
copy "config.json" "C:\Program Files\نظام إدارة محل الغاز\" /Y >nul

REM إنشاء اختصار على سطح المكتب
echo إنشاء اختصار على سطح المكتب...
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\نظام إدارة محل الغاز.lnk'); $Shortcut.TargetPath = 'C:\Program Files\نظام إدارة محل الغاز\نظام إدارة محل تركيب وتصليح الغاز للسيارات.exe'; $Shortcut.Save()"

REM إنشاء اختصار في قائمة ابدأ
echo إنشاء اختصار في قائمة ابدأ...
if not exist "%APPDATA%\Microsoft\Windows\Start Menu\Programs\نظام إدارة محل الغاز" (
    mkdir "%APPDATA%\Microsoft\Windows\Start Menu\Programs\نظام إدارة محل الغاز"
)
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%APPDATA%\Microsoft\Windows\Start Menu\Programs\نظام إدارة محل الغاز\نظام إدارة محل الغاز.lnk'); $Shortcut.TargetPath = 'C:\Program Files\نظام إدارة محل الغاز\نظام إدارة محل تركيب وتصليح الغاز للسيارات.exe'; $Shortcut.Save()"

echo.
echo ========================================
echo    تم تثبيت البرنامج بنجاح!
echo ========================================
echo.
echo يمكنك الآن تشغيل البرنامج من:
echo - سطح المكتب
echo - قائمة ابدأ
echo.
echo اضغط أي مفتاح للخروج...
pause >nul
