@echo off
echo ========================================
echo    Gas Shop Management System Installer
echo ========================================
echo.

echo Checking system requirements...
echo.

REM Create program directory
if not exist "C:\Program Files\GasShopManagement" (
    echo Creating program directory...
    mkdir "C:\Program Files\GasShopManagement"
)

REM Create assets directory
if not exist "C:\Program Files\GasShopManagement\assets" (
    mkdir "C:\Program Files\GasShopManagement\assets"
)

REM Copy files
echo Copying program files...
xcopy "dist\win-unpacked\*" "C:\Program Files\GasShopManagement\" /Y /E >nul
xcopy "assets\*" "C:\Program Files\GasShopManagement\assets\" /Y /E >nul
copy "config.json" "C:\Program Files\GasShopManagement\" /Y >nul

REM Create desktop shortcut
echo Creating desktop shortcut...
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\Gas Shop Management.lnk'); $Shortcut.TargetPath = 'C:\Program Files\GasShopManagement\نظام إدارة محل تركيب وتصليح الغاز للسيارات.exe'; $Shortcut.Save()"

REM Create start menu shortcut
echo Creating start menu shortcut...
if not exist "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Gas Shop Management" (
    mkdir "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Gas Shop Management"
)
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%APPDATA%\Microsoft\Windows\Start Menu\Programs\Gas Shop Management\Gas Shop Management.lnk'); $Shortcut.TargetPath = 'C:\Program Files\GasShopManagement\نظام إدارة محل تركيب وتصليح الغاز للسيارات.exe'; $Shortcut.Save()"

echo.
echo ========================================
echo    Installation completed successfully!
echo ========================================
echo.
echo You can now run the program from:
echo - Desktop shortcut
echo - Start menu
echo.
echo Press any key to exit...
pause >nul
