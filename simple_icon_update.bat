@echo off
setlocal enabledelayedexpansion
title Gas Shop Management System - Simple Icon Update

echo ========================================
echo   Gas Shop Management System
echo   Simple Icon Update
echo ========================================
echo.

set "INSTALL_DIR=%USERPROFILE%\AppData\Local\GasShopManagement"
set "DESKTOP_SHORTCUT=%USERPROFILE%\Desktop\Gas Shop Management.lnk"
set "STARTMENU_DIR=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Gas Shop Management"

echo Checking installation...
if not exist "%INSTALL_DIR%" (
    echo Error: Application not found. Please run the installer first.
    pause
    exit /b 1
)

echo Creating simple icon file...

REM Create a simple icon using echo commands (creates a basic ICO file)
echo Creating icon placeholder...
echo. > "%INSTALL_DIR%\assets\icon.txt"

echo Updating shortcuts without custom icon...

REM Remove old shortcuts
del "%DESKTOP_SHORTCUT%" >nul 2>&1
rmdir /s /q "%STARTMENU_DIR%" >nul 2>&1

echo Creating new desktop shortcut...
REM Create new desktop shortcut
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP_SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\launch_silent.vbs'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Gas Shop Management System - Future Fuel Corporation'; $Shortcut.Save()" 2>nul

echo Creating new Start Menu shortcuts...
REM Create Start Menu directory
if not exist "%STARTMENU_DIR%" mkdir "%STARTMENU_DIR%"

REM Create main shortcut
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%STARTMENU_DIR%\Gas Shop Management.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\launch_silent.vbs'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Gas Shop Management System'; $Shortcut.Save()" 2>nul

REM Create web version shortcut
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%STARTMENU_DIR%\Gas Shop Management (Web).lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\index.html'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Gas Shop Management System - Web Version'; $Shortcut.Save()" 2>nul

REM Create uninstaller shortcut
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%STARTMENU_DIR%\Uninstall Gas Shop Management.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\uninstall.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Uninstall Gas Shop Management System'; $Shortcut.Save()" 2>nul

echo.
echo ========================================
echo   Update completed successfully!
echo ========================================
echo.
echo Updated shortcuts:
echo ✓ Desktop: Gas Shop Management
echo ✓ Start Menu: Gas Shop Management
echo ✓ Start Menu: Gas Shop Management (Web)
echo ✓ Start Menu: Uninstall Gas Shop Management
echo.
echo To add a custom icon:
echo 1. Save your icon as 'icon.ico' in: %INSTALL_DIR%\assets\
echo 2. Right-click on the desktop shortcut
echo 3. Select Properties ^> Change Icon
echo 4. Browse to the icon file
echo.
echo Press any key to test the application...
pause >nul

echo Testing application launch...
start "" "%INSTALL_DIR%\launch_silent.vbs"

echo.
echo Application should now be running!
echo Check your browser or desktop for the application window.
echo.
echo Press any key to exit...
pause >nul
