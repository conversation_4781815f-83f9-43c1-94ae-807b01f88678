{"name": "gas-shop-management", "productName": "نظام إدارة محل تركيب وتصليح الغاز للسيارات", "version": "1.0.0", "description": "نظام متكامل لإدارة محل تركيب وتصليح الغاز للسيارات", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder", "pack": "electron-builder --dir", "dist": "electron-builder", "postinstall": "electron-builder install-app-deps"}, "keywords": ["gas", "shop", "management", "electron", "desktop"], "author": {"name": "Your Name", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"electron": "^28.1.0", "electron-builder": "^24.9.1"}, "dependencies": {}, "build": {"appId": "com.example.gasshopmanagement", "productName": "نظام إدارة محل تركيب وتصليح الغاز للسيارات", "directories": {"output": "dist"}, "files": ["**/*", "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!**/node_modules/*.d.ts", "!**/node_modules/.bin", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"], "win": {"target": ["nsis"], "icon": "icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "نظام إدارة محل الغاز", "artifactName": "GasShopManagement-Setup-${version}.${ext}", "uninstallDisplayName": "نظام إدارة محل تركيب وتصليح الغاز للسيارات", "installerIcon": "icon.ico", "uninstallerIcon": "icon.ico", "installerHeaderIcon": "icon.ico", "displayLanguageSelector": true, "multiLanguageInstaller": true, "language": "1025", "license": "LICENSE"}}}