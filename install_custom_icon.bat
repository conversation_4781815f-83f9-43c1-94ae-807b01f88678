@echo off
setlocal enabledelayedexpansion
title شركة وقود المستقبل - تثبيت الأيقونة المخصصة

echo ========================================
echo   شركة وقود المستقبل
echo   تثبيت الأيقونة المخصصة
echo ========================================
echo.

set "INSTALL_DIR=%USERPROFILE%\AppData\Local\GasShopManagement"
set "DESKTOP_SHORTCUT=%USERPROFILE%\Desktop\Gas Shop Management.lnk"
set "STARTMENU_DIR=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Gas Shop Management"

echo التحقق من وجود التطبيق...
if not exist "%INSTALL_DIR%" (
    echo خطأ: التطبيق غير مثبت. يرجى تشغيل المثبت الرئيسي أولاً.
    pause
    exit /b 1
)

echo البحث عن ملفات الأيقونة...

REM Check for downloaded icon files in current directory
set "ICON_FOUND=0"
if exist "future-fuel-icon.png" (
    echo تم العثور على: future-fuel-icon.png
    copy "future-fuel-icon.png" "%INSTALL_DIR%\assets\icon.png" >nul 2>&1
    set "ICON_FOUND=1"
)

if exist "icon.ico" (
    echo تم العثور على: icon.ico
    copy "icon.ico" "%INSTALL_DIR%\assets\icon.ico" >nul 2>&1
    set "ICON_FOUND=1"
)

if exist "future-fuel-icon-32.ico" (
    echo تم العثور على: future-fuel-icon-32.ico
    copy "future-fuel-icon-32.ico" "%INSTALL_DIR%\assets\icon.ico" >nul 2>&1
    set "ICON_FOUND=1"
)

REM Check Downloads folder
if exist "%USERPROFILE%\Downloads\future-fuel-icon.png" (
    echo تم العثور على الأيقونة في مجلد التنزيلات
    copy "%USERPROFILE%\Downloads\future-fuel-icon.png" "%INSTALL_DIR%\assets\icon.png" >nul 2>&1
    set "ICON_FOUND=1"
)

if exist "%USERPROFILE%\Downloads\icon.ico" (
    echo تم العثور على ملف ICO في مجلد التنزيلات
    copy "%USERPROFILE%\Downloads\icon.ico" "%INSTALL_DIR%\assets\icon.ico" >nul 2>&1
    set "ICON_FOUND=1"
)

if "%ICON_FOUND%"=="0" (
    echo لم يتم العثور على ملفات الأيقونة.
    echo يرجى:
    echo 1. تنزيل الأيقونة من أداة التحويل
    echo 2. حفظها في نفس مجلد هذا الملف
    echo 3. إعادة تشغيل هذا المثبت
    echo.
    pause
    exit /b 1
)

echo تحديث الاختصارات بالأيقونة الجديدة...

REM Remove old shortcuts
del "%DESKTOP_SHORTCUT%" >nul 2>&1
rmdir /s /q "%STARTMENU_DIR%" >nul 2>&1

echo إنشاء اختصار سطح المكتب الجديد...
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP_SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\launch_silent.vbs'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.IconLocation = '%INSTALL_DIR%\assets\icon.ico'; $Shortcut.Description = 'شركة وقود المستقبل - نظام إدارة محل الغاز'; $Shortcut.Save()" 2>nul

echo إنشاء اختصارات قائمة ابدأ...
if not exist "%STARTMENU_DIR%" mkdir "%STARTMENU_DIR%"

REM Main application shortcut
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%STARTMENU_DIR%\شركة وقود المستقبل.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\launch_silent.vbs'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.IconLocation = '%INSTALL_DIR%\assets\icon.ico'; $Shortcut.Description = 'شركة وقود المستقبل - نظام إدارة محل الغاز'; $Shortcut.Save()" 2>nul

REM Web version shortcut
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%STARTMENU_DIR%\شركة وقود المستقبل (ويب).lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\index.html'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.IconLocation = '%INSTALL_DIR%\assets\icon.ico'; $Shortcut.Description = 'شركة وقود المستقبل - النسخة الويب'; $Shortcut.Save()" 2>nul

REM Uninstaller shortcut
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%STARTMENU_DIR%\إلغاء تثبيت شركة وقود المستقبل.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\uninstall.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'إلغاء تثبيت شركة وقود المستقبل'; $Shortcut.Save()" 2>nul

echo.
echo ========================================
echo   تم تثبيت الأيقونة المخصصة بنجاح!
echo ========================================
echo.
echo ✓ تم نسخ ملفات الأيقونة إلى مجلد التطبيق
echo ✓ تم تحديث اختصار سطح المكتب
echo ✓ تم تحديث اختصارات قائمة ابدأ
echo.
echo الاختصارات الجديدة:
echo - سطح المكتب: Gas Shop Management (بأيقونة مخصصة)
echo - قائمة ابدأ: شركة وقود المستقبل (بأيقونة مخصصة)
echo - قائمة ابدأ: شركة وقود المستقبل (ويب)
echo - قائمة ابدأ: إلغاء تثبيت شركة وقود المستقبل
echo.
echo الآن التطبيق يحمل شعار شركتك الرسمي!
echo.
echo هل تريد تشغيل التطبيق لرؤية الأيقونة الجديدة؟ (Y/N)
choice /c YN /n
if %errorlevel% equ 1 (
    echo تشغيل التطبيق...
    start "" "%INSTALL_DIR%\launch_silent.vbs"
    echo.
    echo تم تشغيل التطبيق! تحقق من الاختصار الجديد على سطح المكتب.
)

echo.
echo اضغط أي مفتاح للخروج...
pause >nul
